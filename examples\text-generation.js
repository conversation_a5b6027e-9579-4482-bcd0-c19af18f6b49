#!/usr/bin/env node

/**
 * Text Generation Examples
 * 
 * This file demonstrates various text generation capabilities
 * of the Gemini CLI toolbox.
 */

import { generateText, summarizeText, translateText, rewriteText } from '../src/modules/textGeneration.js'

async function runTextGenerationExamples() {
  console.log('🤖 Text Generation Examples\n')

  try {
    // Example 1: Basic text generation
    console.log('1. Basic Text Generation')
    console.log('Prompt: "Write a short story about a robot learning to paint"')
    
    const story = await generateText('Write a short story about a robot learning to paint', {
      model: 'gpt-3.5-turbo',
      temperature: 0.8,
      maxLength: 500
    })
    
    console.log('Generated Story:')
    console.log(story.text)
    console.log(`Model: ${story.model}, Tokens: ${story.usage?.total_tokens || 'N/A'}\n`)

    // Example 2: Text summarization
    console.log('2. Text Summarization')
    const longText = `
      Artificial Intelligence (AI) has become one of the most transformative technologies of the 21st century. 
      From machine learning algorithms that power recommendation systems to natural language processing models 
      that enable chatbots and virtual assistants, AI is reshaping industries and changing how we interact with technology.
      
      The field of AI encompasses various subdomains including machine learning, deep learning, computer vision, 
      natural language processing, and robotics. Each of these areas has seen significant advances in recent years, 
      driven by improvements in computational power, the availability of large datasets, and algorithmic innovations.
      
      However, the rapid advancement of AI also raises important questions about ethics, privacy, job displacement, 
      and the need for responsible AI development. As AI systems become more capable and autonomous, it becomes 
      increasingly important to ensure they are developed and deployed in ways that benefit humanity while 
      minimizing potential risks and negative consequences.
    `
    
    const summary = await summarizeText(longText, {
      length: 'short',
      model: 'gpt-3.5-turbo'
    })
    
    console.log('Original text length:', longText.length, 'characters')
    console.log('Summary:')
    console.log(summary.text)
    console.log(`Compression ratio: ${summary.compressionRatio}\n`)

    // Example 3: Text translation
    console.log('3. Text Translation')
    const englishText = 'Hello, welcome to our AI-powered translation service. We hope you find it useful!'
    
    const spanishTranslation = await translateText(englishText, 'Spanish', {
      model: 'gpt-3.5-turbo'
    })
    
    console.log('Original (English):', englishText)
    console.log('Translation (Spanish):', spanishTranslation.text)
    console.log()

    // Example 4: Text rewriting with different styles
    console.log('4. Text Rewriting')
    const originalText = 'The meeting will be held tomorrow at 3 PM in the conference room.'
    
    const formalRewrite = await rewriteText(originalText, 'formal and professional', {
      model: 'gpt-3.5-turbo'
    })
    
    const casualRewrite = await rewriteText(originalText, 'casual and friendly', {
      model: 'gpt-3.5-turbo'
    })
    
    console.log('Original:', originalText)
    console.log('Formal style:', formalRewrite.text)
    console.log('Casual style:', casualRewrite.text)
    console.log()

    // Example 5: Creative writing with different temperatures
    console.log('5. Temperature Comparison')
    const prompt = 'Describe a magical forest'
    
    const conservativeResponse = await generateText(prompt, {
      temperature: 0.2,
      maxLength: 200
    })
    
    const creativeResponse = await generateText(prompt, {
      temperature: 0.9,
      maxLength: 200
    })
    
    console.log('Conservative (temp=0.2):')
    console.log(conservativeResponse.text)
    console.log('\nCreative (temp=0.9):')
    console.log(creativeResponse.text)
    console.log()

    // Example 6: Multiple model comparison
    console.log('6. Model Comparison')
    const comparisonPrompt = 'Explain quantum computing in simple terms'
    
    try {
      const gptResponse = await generateText(comparisonPrompt, {
        model: 'gpt-3.5-turbo',
        maxLength: 150
      })
      
      console.log('GPT-3.5 Response:')
      console.log(gptResponse.text)
      console.log()
    } catch (error) {
      console.log('GPT-3.5 not available:', error.message)
    }

    try {
      const claudeResponse = await generateText(comparisonPrompt, {
        model: 'claude-3-haiku',
        maxLength: 150
      })
      
      console.log('Claude Response:')
      console.log(claudeResponse.text)
      console.log()
    } catch (error) {
      console.log('Claude not available:', error.message)
    }

  } catch (error) {
    console.error('❌ Error in text generation examples:', error.message)
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTextGenerationExamples()
    .then(() => {
      console.log('✅ Text generation examples completed!')
    })
    .catch((error) => {
      console.error('❌ Examples failed:', error)
      process.exit(1)
    })
}

export { runTextGenerationExamples }
