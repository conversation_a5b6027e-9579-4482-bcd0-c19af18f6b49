import fs from 'fs/promises'
import path from 'path'
import pdfParse from 'pdf-parse'
import mammoth from 'mammoth'
import { marked } from 'marked'
import { JSDOM } from 'jsdom'
import { generateText } from './textGeneration.js'
import { config } from '../config/index.js'
import { logger } from '../utils/logger.js'
import { readFile, getFileExtension } from '../utils/filesystem.js'

/**
 * Extract text from PDF file
 */
async function extractFromPDF (filePath) {
  try {
    const dataBuffer = await fs.readFile(filePath)
    const data = await pdfParse(dataBuffer)
    return {
      text: data.text,
      pages: data.numpages,
      info: data.info
    }
  } catch (error) {
    logger.error(`Failed to extract from PDF ${filePath}:`, error)
    throw new Error(`PDF extraction failed: ${error.message}`)
  }
}

/**
 * Extract text from Word document
 */
async function extractFromWord (filePath) {
  try {
    const result = await mammoth.extractRawText({ path: filePath })
    return {
      text: result.value,
      messages: result.messages
    }
  } catch (error) {
    logger.error(`Failed to extract from Word document ${filePath}:`, error)
    throw new Error(`Word document extraction failed: ${error.message}`)
  }
}

/**
 * Extract text from HTML file
 */
async function extractFromHTML (filePath) {
  try {
    const htmlContent = await readFile(filePath)
    const dom = new JSDOM(htmlContent)
    const text = dom.window.document.body.textContent || ''
    
    return {
      text: text.trim(),
      title: dom.window.document.title || ''
    }
  } catch (error) {
    logger.error(`Failed to extract from HTML ${filePath}:`, error)
    throw new Error(`HTML extraction failed: ${error.message}`)
  }
}

/**
 * Extract text from Markdown file
 */
async function extractFromMarkdown (filePath) {
  try {
    const markdownContent = await readFile(filePath)
    const htmlContent = marked(markdownContent)
    const dom = new JSDOM(htmlContent)
    const text = dom.window.document.body.textContent || ''
    
    return {
      text: text.trim(),
      markdown: markdownContent
    }
  } catch (error) {
    logger.error(`Failed to extract from Markdown ${filePath}:`, error)
    throw new Error(`Markdown extraction failed: ${error.message}`)
  }
}

/**
 * Extract text from JSON file
 */
async function extractFromJSON (filePath) {
  try {
    const jsonContent = await readFile(filePath)
    const data = JSON.parse(jsonContent)
    
    // Convert JSON to readable text
    const text = JSON.stringify(data, null, 2)
    
    return {
      text,
      data,
      keys: Object.keys(data)
    }
  } catch (error) {
    logger.error(`Failed to extract from JSON ${filePath}:`, error)
    throw new Error(`JSON extraction failed: ${error.message}`)
  }
}

/**
 * Extract text from plain text file
 */
async function extractFromText (filePath) {
  try {
    const text = await readFile(filePath)
    return {
      text,
      lines: text.split('\n').length,
      words: text.split(/\s+/).length
    }
  } catch (error) {
    logger.error(`Failed to extract from text file ${filePath}:`, error)
    throw new Error(`Text extraction failed: ${error.message}`)
  }
}

/**
 * Extract text from various document formats
 */
export async function extractText (filePath) {
  const extension = getFileExtension(filePath)
  
  logger.info(`Extracting text from ${filePath} (${extension})`)
  
  switch (extension) {
    case '.pdf':
      return await extractFromPDF(filePath)
    case '.doc':
    case '.docx':
      return await extractFromWord(filePath)
    case '.html':
    case '.htm':
      return await extractFromHTML(filePath)
    case '.md':
    case '.markdown':
      return await extractFromMarkdown(filePath)
    case '.json':
      return await extractFromJSON(filePath)
    case '.txt':
    case '.text':
      return await extractFromText(filePath)
    default:
      // Try as plain text
      return await extractFromText(filePath)
  }
}

/**
 * Summarize document content
 */
export async function summarizeDocument (text, options = {}) {
  const length = options.length || 'medium' // short, medium, long
  const style = options.style || 'bullet-points' // paragraph, bullet-points, key-points
  
  let lengthInstruction
  switch (length) {
    case 'short':
      lengthInstruction = 'in 2-3 sentences'
      break
    case 'long':
      lengthInstruction = 'in 1-2 paragraphs with detailed analysis'
      break
    default:
      lengthInstruction = 'in 4-6 sentences'
  }
  
  let styleInstruction
  switch (style) {
    case 'bullet-points':
      styleInstruction = 'Format as bullet points highlighting key information.'
      break
    case 'key-points':
      styleInstruction = 'Focus on the most important key points and takeaways.'
      break
    default:
      styleInstruction = 'Write in clear, coherent paragraphs.'
  }
  
  const prompt = `Summarize the following document ${lengthInstruction}:

${text}

${styleInstruction}

Focus on the main ideas, key findings, and important details.`

  const systemPrompt = 'You are an expert at creating accurate, concise summaries that capture the essential information from documents.'

  try {
    const result = await generateText(prompt, {
      ...options,
      systemPrompt,
      temperature: 0.3,
      model: options.model || config.models.text.default
    })

    return {
      summary: result.text,
      originalLength: text.length,
      summaryLength: result.text.length,
      compressionRatio: (result.text.length / text.length * 100).toFixed(1) + '%',
      model: result.model
    }
  } catch (error) {
    logger.error('Document summarization failed:', error)
    throw error
  }
}

/**
 * Extract key information from document
 */
export async function extractKeyInformation (text, options = {}) {
  const extractionType = options.type || 'general' // general, entities, dates, numbers, contacts
  
  let prompt
  switch (extractionType) {
    case 'entities':
      prompt = `Extract all named entities (people, organizations, locations) from the following text:

${text}

Format as:
- People: [list]
- Organizations: [list]
- Locations: [list]`
      break
    case 'dates':
      prompt = `Extract all dates and time references from the following text:

${text}

List all dates found with their context.`
      break
    case 'numbers':
      prompt = `Extract all important numbers, statistics, and financial figures from the following text:

${text}

Include the context for each number.`
      break
    case 'contacts':
      prompt = `Extract all contact information (emails, phone numbers, addresses) from the following text:

${text}

Format clearly with labels.`
      break
    default:
      prompt = `Extract the key information from the following document:

${text}

Identify and list:
- Main topics and themes
- Important facts and figures
- Key people and organizations
- Significant dates and events
- Action items or conclusions`
  }

  const systemPrompt = 'You are an expert at information extraction. Identify and organize key information accurately and systematically.'

  try {
    const result = await generateText(prompt, {
      ...options,
      systemPrompt,
      temperature: 0.2,
      model: options.model || config.models.text.default
    })

    return {
      extractedInfo: result.text,
      extractionType,
      model: result.model
    }
  } catch (error) {
    logger.error('Information extraction failed:', error)
    throw error
  }
}

/**
 * Translate document content
 */
export async function translateDocument (text, targetLanguage, options = {}) {
  const prompt = `Translate the following document to ${targetLanguage}. Maintain the original structure, formatting, and meaning:

${text}`

  const systemPrompt = `You are a professional translator. Translate accurately while preserving the document's structure, tone, and technical terminology. Maintain any formatting markers.`

  try {
    const result = await generateText(prompt, {
      ...options,
      systemPrompt,
      temperature: 0.2,
      model: options.model || config.models.text.default
    })

    return {
      translatedText: result.text,
      sourceLanguage: 'auto-detected',
      targetLanguage,
      model: result.model
    }
  } catch (error) {
    logger.error('Document translation failed:', error)
    throw error
  }
}

/**
 * Analyze document sentiment and tone
 */
export async function analyzeDocumentSentiment (text, options = {}) {
  const prompt = `Analyze the sentiment and tone of the following document:

${text}

Provide:
1. Overall sentiment (positive, negative, neutral)
2. Tone analysis (formal, informal, professional, emotional, etc.)
3. Key emotional indicators
4. Confidence level of analysis
5. Notable patterns or themes`

  const systemPrompt = 'You are an expert in sentiment analysis and linguistic tone assessment. Provide detailed, accurate analysis of text sentiment and emotional content.'

  try {
    const result = await generateText(prompt, {
      ...options,
      systemPrompt,
      temperature: 0.3,
      model: options.model || config.models.text.default
    })

    return {
      analysis: result.text,
      model: result.model
    }
  } catch (error) {
    logger.error('Sentiment analysis failed:', error)
    throw error
  }
}

/**
 * Main document processing function
 */
export async function processDocument (filePath, options = {}) {
  const action = options.action || 'summarize'
  
  try {
    // Extract text from document
    logger.info(`Processing document: ${filePath} with action: ${action}`)
    const extraction = await extractText(filePath)
    const text = extraction.text
    
    if (!text || text.trim().length === 0) {
      throw new Error('No text content found in document')
    }

    let result
    switch (action) {
      case 'extract':
        return text
      case 'summarize':
        result = await summarizeDocument(text, options)
        return result.summary
      case 'key-info':
        result = await extractKeyInformation(text, options)
        return result.extractedInfo
      case 'translate':
        if (!options.language) {
          throw new Error('Target language is required for translation')
        }
        result = await translateDocument(text, options.language, options)
        return result.translatedText
      case 'sentiment':
        result = await analyzeDocumentSentiment(text, options)
        return result.analysis
      default:
        throw new Error(`Unsupported action: ${action}`)
    }
  } catch (error) {
    logger.error(`Document processing failed for ${filePath}:`, error)
    throw error
  }
}

/**
 * Get supported document formats
 */
export function getSupportedFormats () {
  return [
    '.pdf',
    '.doc',
    '.docx',
    '.html',
    '.htm',
    '.md',
    '.markdown',
    '.txt',
    '.text',
    '.json'
  ]
}
