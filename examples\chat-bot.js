#!/usr/bin/env node

/**
 * Chat Bot Example
 * 
 * This example demonstrates how to build a simple chat bot
 * using the Gemini CLI chat functionality.
 */

import { ChatSession } from '../src/modules/chat.js'
import { logger } from '../src/utils/logger.js'

/**
 * Simple chat bot that responds to user messages
 */
class SimpleChatBot {
  constructor(options = {}) {
    this.session = new ChatSession({
      model: options.model || 'gpt-3.5-turbo',
      system: options.systemPrompt || 'You are a helpful and friendly AI assistant.',
      maxHistory: options.maxHistory || 10
    })
    this.name = options.name || 'Assistant'
  }

  async sendMessage(message) {
    try {
      const response = await this.session.sendMessage(message)
      return response.message
    } catch (error) {
      logger.error('Chat bot error:', error)
      return 'I apologize, but I encountered an error processing your message.'
    }
  }

  getConversationSummary() {
    return this.session.getSummary()
  }

  async saveConversation() {
    await this.session.saveConversation()
  }
}

/**
 * Specialized chat bots with different personalities
 */

// Technical Assistant
class TechnicalAssistant extends SimpleChatBot {
  constructor() {
    super({
      name: '<PERSON>Bot',
      systemPrompt: `You are a technical assistant specializing in programming, software development, and technology. 
      Provide accurate, detailed technical information and code examples when appropriate. 
      Be concise but thorough in your explanations.`,
      model: 'gpt-4'
    })
  }
}

// Creative Writing Assistant
class CreativeAssistant extends SimpleChatBot {
  constructor() {
    super({
      name: 'CreativeBot',
      systemPrompt: `You are a creative writing assistant. Help users with storytelling, poetry, creative writing, 
      and artistic expression. Be imaginative, inspiring, and encourage creativity. 
      Provide constructive feedback and creative suggestions.`,
      model: 'gpt-3.5-turbo',
      maxHistory: 15
    })
  }
}

// Educational Tutor
class EducationalTutor extends SimpleChatBot {
  constructor() {
    super({
      name: 'TutorBot',
      systemPrompt: `You are an educational tutor. Help students learn by explaining concepts clearly, 
      asking guiding questions, and providing examples. Adapt your teaching style to the student's level. 
      Be patient, encouraging, and focus on helping students understand rather than just giving answers.`,
      model: 'gpt-3.5-turbo'
    })
  }
}

/**
 * Multi-bot conversation manager
 */
class ConversationManager {
  constructor() {
    this.bots = {
      technical: new TechnicalAssistant(),
      creative: new CreativeAssistant(),
      tutor: new EducationalTutor(),
      general: new SimpleChatBot()
    }
    this.currentBot = 'general'
  }

  switchBot(botType) {
    if (this.bots[botType]) {
      this.currentBot = botType
      return `Switched to ${this.bots[botType].name}`
    }
    return 'Unknown bot type. Available: technical, creative, tutor, general'
  }

  async sendMessage(message) {
    // Check for bot switching commands
    if (message.toLowerCase().startsWith('/switch ')) {
      const botType = message.toLowerCase().replace('/switch ', '')
      return this.switchBot(botType)
    }

    // Check for help command
    if (message.toLowerCase() === '/help') {
      return `Available commands:
      /switch technical - Switch to technical assistant
      /switch creative - Switch to creative writing assistant  
      /switch tutor - Switch to educational tutor
      /switch general - Switch to general assistant
      /help - Show this help message
      /status - Show current bot and conversation stats
      
      Currently using: ${this.bots[this.currentBot].name}`
    }

    // Check for status command
    if (message.toLowerCase() === '/status') {
      const summary = this.bots[this.currentBot].getConversationSummary()
      return `Current bot: ${this.bots[this.currentBot].name}
      Messages in conversation: ${summary.messageCount}
      Session duration: ${summary.duration} seconds`
    }

    // Send message to current bot
    return await this.bots[this.currentBot].sendMessage(message)
  }

  async saveAllConversations() {
    for (const bot of Object.values(this.bots)) {
      await bot.saveConversation()
    }
  }
}

/**
 * Example usage and demonstrations
 */
async function runChatBotExamples() {
  console.log('🤖 Chat Bot Examples\n')

  try {
    // Example 1: Simple chat bot
    console.log('1. Simple Chat Bot')
    const simpleBot = new SimpleChatBot({
      name: 'SimpleBot',
      systemPrompt: 'You are a friendly assistant who loves to help people.'
    })

    const greeting = await simpleBot.sendMessage('Hello! How are you today?')
    console.log('User: Hello! How are you today?')
    console.log(`Bot: ${greeting}\n`)

    // Example 2: Technical Assistant
    console.log('2. Technical Assistant')
    const techBot = new TechnicalAssistant()
    
    const techQuestion = 'Can you explain what REST APIs are?'
    const techResponse = await techBot.sendMessage(techQuestion)
    console.log(`User: ${techQuestion}`)
    console.log(`TechBot: ${techResponse}\n`)

    // Example 3: Creative Assistant
    console.log('3. Creative Writing Assistant')
    const creativeBot = new CreativeAssistant()
    
    const creativePrompt = 'Help me write the opening line for a mystery novel'
    const creativeResponse = await creativeBot.sendMessage(creativePrompt)
    console.log(`User: ${creativePrompt}`)
    console.log(`CreativeBot: ${creativeResponse}\n`)

    // Example 4: Multi-bot conversation
    console.log('4. Multi-Bot Conversation Manager')
    const manager = new ConversationManager()
    
    // Test help command
    const helpResponse = await manager.sendMessage('/help')
    console.log('User: /help')
    console.log(`Manager: ${helpResponse}\n`)

    // Switch to technical bot
    const switchResponse = await manager.sendMessage('/switch technical')
    console.log('User: /switch technical')
    console.log(`Manager: ${switchResponse}`)

    // Ask technical question
    const question = 'What is the difference between SQL and NoSQL databases?'
    const answer = await manager.sendMessage(question)
    console.log(`User: ${question}`)
    console.log(`Manager: ${answer}\n`)

    // Get status
    const statusResponse = await manager.sendMessage('/status')
    console.log('User: /status')
    console.log(`Manager: ${statusResponse}\n`)

    // Example 5: Conversation persistence
    console.log('5. Conversation Persistence')
    const persistentBot = new SimpleChatBot({
      name: 'PersistentBot'
    })

    await persistentBot.sendMessage('Remember that my favorite color is blue')
    await persistentBot.sendMessage('What programming languages do you recommend?')
    
    const summary = persistentBot.getConversationSummary()
    console.log('Conversation Summary:')
    console.log(`- Session ID: ${summary.sessionId}`)
    console.log(`- Messages: ${summary.messageCount}`)
    console.log(`- Duration: ${summary.duration} seconds`)
    
    // Save conversation
    await persistentBot.saveConversation()
    console.log('✅ Conversation saved to disk\n')

  } catch (error) {
    console.error('❌ Error in chat bot examples:', error.message)
  }
}

// Interactive chat bot demo
async function interactiveChatDemo() {
  console.log('🎯 Interactive Chat Bot Demo')
  console.log('Type "exit" to quit, or try commands like /switch technical, /help\n')

  const manager = new ConversationManager()
  
  // This would typically use readline for real interaction
  // For demo purposes, we'll simulate some interactions
  const demoMessages = [
    'Hello!',
    '/switch technical',
    'How do I deploy a Node.js app?',
    '/switch creative',
    'Write a haiku about programming',
    '/status',
    'exit'
  ]

  for (const message of demoMessages) {
    console.log(`User: ${message}`)
    
    if (message === 'exit') {
      console.log('Bot: Goodbye! Saving conversations...')
      await manager.saveAllConversations()
      break
    }

    const response = await manager.sendMessage(message)
    console.log(`Bot: ${response}\n`)
    
    // Simulate thinking time
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runChatBotExamples()
    .then(() => interactiveChatDemo())
    .then(() => {
      console.log('✅ Chat bot examples completed!')
    })
    .catch((error) => {
      console.error('❌ Examples failed:', error)
      process.exit(1)
    })
}

export { 
  SimpleChatBot, 
  TechnicalAssistant, 
  CreativeAssistant, 
  EducationalTutor, 
  ConversationManager,
  runChatBotExamples 
}
