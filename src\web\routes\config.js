import express from 'express'
import { loadUserConfig, saveUserConfig, getConfigValue, setConfigValue, resetConfig, validateApiKey } from '../../utils/configManager.js'
import { logger } from '../../utils/logger.js'

const router = express.Router()

/**
 * Get current configuration
 * GET /api/config
 */
router.get('/', async (req, res) => {
  try {
    const userConfig = await loadUserConfig()
    
    // Mask API keys for security
    const maskedConfig = {
      ...userConfig,
      apiKeys: Object.fromEntries(
        Object.entries(userConfig.apiKeys).map(([key, value]) => [
          key,
          value ? `${value.substring(0, 8)}...` : ''
        ])
      )
    }

    res.json({
      success: true,
      data: maskedConfig
    })
  } catch (error) {
    logger.error('Get config API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Update configuration
 * PUT /api/config
 */
router.put('/', async (req, res) => {
  try {
    const updates = req.body

    if (!updates || typeof updates !== 'object') {
      return res.status(400).json({
        error: 'Configuration updates are required'
      })
    }

    const currentConfig = await loadUserConfig()
    const newConfig = { ...currentConfig, ...updates }

    // Validate API keys if provided
    if (updates.apiKeys) {
      for (const [provider, key] of Object.entries(updates.apiKeys)) {
        if (key && !validateApiKey(provider, key)) {
          return res.status(400).json({
            error: `Invalid API key format for ${provider}`
          })
        }
      }
    }

    await saveUserConfig(newConfig)

    res.json({
      success: true,
      data: {
        message: 'Configuration updated successfully'
      }
    })
  } catch (error) {
    logger.error('Update config API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Get specific configuration value
 * GET /api/config/:key
 */
router.get('/:key', async (req, res) => {
  try {
    const { key } = req.params
    const value = await getConfigValue(key)

    if (value === undefined) {
      return res.status(404).json({
        error: 'Configuration key not found'
      })
    }

    // Mask API keys
    let maskedValue = value
    if (key.includes('apiKey') && typeof value === 'string' && value.length > 8) {
      maskedValue = `${value.substring(0, 8)}...`
    }

    res.json({
      success: true,
      data: {
        key,
        value: maskedValue
      }
    })
  } catch (error) {
    logger.error('Get config value API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Set specific configuration value
 * PUT /api/config/:key
 */
router.put('/:key', async (req, res) => {
  try {
    const { key } = req.params
    const { value } = req.body

    if (value === undefined) {
      return res.status(400).json({
        error: 'Value is required'
      })
    }

    // Validate API keys
    if (key.includes('apiKey')) {
      const provider = key.split('.')[1] // e.g., apiKeys.openai -> openai
      if (value && !validateApiKey(provider, value)) {
        return res.status(400).json({
          error: `Invalid API key format for ${provider}`
        })
      }
    }

    await setConfigValue(key, value)

    res.json({
      success: true,
      data: {
        key,
        message: 'Configuration value updated successfully'
      }
    })
  } catch (error) {
    logger.error('Set config value API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Reset configuration to defaults
 * POST /api/config/reset
 */
router.post('/reset', async (req, res) => {
  try {
    await resetConfig()

    res.json({
      success: true,
      data: {
        message: 'Configuration reset to defaults'
      }
    })
  } catch (error) {
    logger.error('Reset config API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Validate API key
 * POST /api/config/validate-key
 */
router.post('/validate-key', async (req, res) => {
  try {
    const { provider, key } = req.body

    if (!provider || !key) {
      return res.status(400).json({
        error: 'Provider and key are required'
      })
    }

    const isValid = validateApiKey(provider, key)

    res.json({
      success: true,
      data: {
        provider,
        valid: isValid
      }
    })
  } catch (error) {
    logger.error('Validate API key error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Test API key by making a simple request
 * POST /api/config/test-key
 */
router.post('/test-key', async (req, res) => {
  try {
    const { provider, key } = req.body

    if (!provider || !key) {
      return res.status(400).json({
        error: 'Provider and key are required'
      })
    }

    let testResult = false
    let error = null

    try {
      switch (provider) {
        case 'openai':
          const OpenAI = (await import('openai')).default
          const openai = new OpenAI({ apiKey: key })
          await openai.models.list()
          testResult = true
          break
        case 'anthropic':
          const Anthropic = (await import('@anthropic-ai/sdk')).default
          const anthropic = new Anthropic({ apiKey: key })
          // Simple test - this will fail if key is invalid
          await anthropic.messages.create({
            model: 'claude-3-haiku-20240307',
            max_tokens: 1,
            messages: [{ role: 'user', content: 'test' }]
          })
          testResult = true
          break
        default:
          error = `Testing not implemented for provider: ${provider}`
      }
    } catch (testError) {
      error = testError.message
    }

    res.json({
      success: true,
      data: {
        provider,
        valid: testResult,
        error
      }
    })
  } catch (error) {
    logger.error('Test API key error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

export default router
