import { jest } from '@jest/globals'
import request from 'supertest'
import createApp from '../../src/web/server.js'

describe('API Integration Tests', () => {
  let app

  beforeAll(async () => {
    app = createApp()
  })

  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200)

      expect(response.body).toHaveProperty('status', 'healthy')
      expect(response.body).toHaveProperty('timestamp')
      expect(response.body).toHaveProperty('version')
      expect(response.body).toHaveProperty('uptime')
    })
  })

  describe('API Info', () => {
    it('should return API information', async () => {
      const response = await request(app)
        .get('/api')
        .expect(200)

      expect(response.body).toHaveProperty('name')
      expect(response.body).toHaveProperty('version')
      expect(response.body).toHaveProperty('description')
      expect(response.body).toHaveProperty('endpoints')
      expect(response.body.endpoints).toHaveProperty('text')
      expect(response.body.endpoints).toHaveProperty('image')
      expect(response.body.endpoints).toHaveProperty('code')
      expect(response.body.endpoints).toHaveProperty('document')
      expect(response.body.endpoints).toHaveProperty('chat')
    })
  })

  describe('Text Generation API', () => {
    it('should generate text successfully', async () => {
      const response = await request(app)
        .post('/api/text/generate')
        .send({
          prompt: 'Write a short poem about AI',
          model: 'gpt-3.5-turbo',
          temperature: 0.7
        })
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body).toHaveProperty('data')
      expect(response.body.data).toHaveProperty('text')
      expect(response.body.data).toHaveProperty('model')
    })

    it('should return error for missing prompt', async () => {
      const response = await request(app)
        .post('/api/text/generate')
        .send({
          model: 'gpt-3.5-turbo'
        })
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('Prompt is required')
    })

    it('should get available models', async () => {
      const response = await request(app)
        .get('/api/text/models')
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body).toHaveProperty('data')
      expect(response.body.data).toHaveProperty('openai')
      expect(response.body.data).toHaveProperty('anthropic')
    })
  })

  describe('Chat API', () => {
    let sessionId

    it('should start a new chat session', async () => {
      const response = await request(app)
        .post('/api/chat/start')
        .send({
          model: 'gpt-3.5-turbo',
          systemPrompt: 'You are a helpful assistant'
        })
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body.data).toHaveProperty('sessionId')
      expect(response.body.data).toHaveProperty('model')
      
      sessionId = response.body.data.sessionId
    })

    it('should send message to chat session', async () => {
      const response = await request(app)
        .post('/api/chat/message')
        .send({
          sessionId,
          message: 'Hello, how are you?'
        })
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body.data).toHaveProperty('message')
      expect(response.body.data).toHaveProperty('sessionId', sessionId)
    })

    it('should get chat history', async () => {
      const response = await request(app)
        .get(`/api/chat/${sessionId}/history`)
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body.data).toHaveProperty('history')
      expect(response.body.data).toHaveProperty('sessionId', sessionId)
      expect(Array.isArray(response.body.data.history)).toBe(true)
    })

    it('should get chat summary', async () => {
      const response = await request(app)
        .get(`/api/chat/${sessionId}/summary`)
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body.data).toHaveProperty('sessionId', sessionId)
      expect(response.body.data).toHaveProperty('messageCount')
    })

    it('should return error for invalid session', async () => {
      const response = await request(app)
        .post('/api/chat/message')
        .send({
          sessionId: 'invalid-session-id',
          message: 'Hello'
        })
        .expect(404)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('Configuration API', () => {
    it('should get current configuration', async () => {
      const response = await request(app)
        .get('/api/config')
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body).toHaveProperty('data')
      expect(response.body.data).toHaveProperty('preferences')
      expect(response.body.data).toHaveProperty('apiKeys')
      expect(response.body.data).toHaveProperty('ui')
    })

    it('should validate API key format', async () => {
      const response = await request(app)
        .post('/api/config/validate-key')
        .send({
          provider: 'openai',
          key: 'invalid-key'
        })
        .expect(200)

      expect(response.body).toHaveProperty('success', true)
      expect(response.body.data).toHaveProperty('valid')
      expect(response.body.data).toHaveProperty('provider', 'openai')
    })

    it('should return error for missing validation parameters', async () => {
      const response = await request(app)
        .post('/api/config/validate-key')
        .send({
          provider: 'openai'
        })
        .expect(400)

      expect(response.body).toHaveProperty('error')
    })
  })

  describe('Error Handling', () => {
    it('should return 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/api/unknown-endpoint')
        .expect(404)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('not found')
    })

    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/text/generate')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400)
    })
  })
})
