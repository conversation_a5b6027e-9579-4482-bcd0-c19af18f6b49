<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini CLI - AI Toolbox</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card {
            transition: transform 0.2s ease-in-out;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .loading {
            display: none;
        }
        .loading.active {
            display: inline-block;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-robot text-3xl mr-3"></i>
                    <h1 class="text-2xl font-bold">Gemini CLI</h1>
                    <span class="ml-3 text-sm opacity-75">AI Toolbox</span>
                </div>
                <nav class="flex space-x-6">
                    <a href="#" class="hover:text-gray-200 transition-colors" onclick="showSection('dashboard')">Dashboard</a>
                    <a href="#" class="hover:text-gray-200 transition-colors" onclick="showSection('chat')">Chat</a>
                    <a href="#" class="hover:text-gray-200 transition-colors" onclick="showSection('config')">Settings</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Dashboard Section -->
        <div id="dashboard" class="section">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">AI Tools Dashboard</h2>
                <p class="text-gray-600">Choose from our comprehensive suite of AI-powered tools</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Text Generation -->
                <div class="card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-pen-fancy text-blue-500 text-2xl mr-3"></i>
                        <h3 class="text-xl font-semibold">Text Generation</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Generate, summarize, translate, and rewrite text using advanced AI models.</p>
                    <button onclick="showTextTools()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors">
                        Open Tools
                    </button>
                </div>

                <!-- Image Generation -->
                <div class="card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-image text-green-500 text-2xl mr-3"></i>
                        <h3 class="text-xl font-semibold">Image Generation</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Create stunning images from text descriptions using DALL-E and Stable Diffusion.</p>
                    <button onclick="showImageTools()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors">
                        Open Tools
                    </button>
                </div>

                <!-- Code Generation -->
                <div class="card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-code text-purple-500 text-2xl mr-3"></i>
                        <h3 class="text-xl font-semibold">Code Generation</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Generate, review, document, and test code in multiple programming languages.</p>
                    <button onclick="showCodeTools()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded transition-colors">
                        Open Tools
                    </button>
                </div>

                <!-- Document Processing -->
                <div class="card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-file-alt text-orange-500 text-2xl mr-3"></i>
                        <h3 class="text-xl font-semibold">Document Processing</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Process PDFs, Word docs, and other documents with AI-powered analysis.</p>
                    <button onclick="showDocumentTools()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded transition-colors">
                        Open Tools
                    </button>
                </div>

                <!-- Chat Interface -->
                <div class="card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-comments text-indigo-500 text-2xl mr-3"></i>
                        <h3 class="text-xl font-semibold">AI Chat</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Have interactive conversations with AI assistants and save chat history.</p>
                    <button onclick="showSection('chat')" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded transition-colors">
                        Start Chat
                    </button>
                </div>

                <!-- API Status -->
                <div class="card bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-server text-red-500 text-2xl mr-3"></i>
                        <h3 class="text-xl font-semibold">API Status</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Monitor API health and configure your AI service connections.</p>
                    <button onclick="checkApiStatus()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded transition-colors">
                        Check Status
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Section -->
        <div id="chat" class="section hidden">
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b">
                    <h2 class="text-2xl font-bold text-gray-800">AI Chat</h2>
                    <p class="text-gray-600">Have a conversation with AI</p>
                </div>
                <div id="chatMessages" class="p-6 h-96 overflow-y-auto bg-gray-50">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-robot text-4xl mb-2"></i>
                        <p>Start a conversation by typing a message below</p>
                    </div>
                </div>
                <div class="p-6 border-t">
                    <div class="flex space-x-4">
                        <input type="text" id="chatInput" placeholder="Type your message..." 
                               class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button onclick="sendMessage()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                            <i class="fas fa-paper-plane mr-2"></i>Send
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuration Section -->
        <div id="config" class="section hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Configuration</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold mb-4">API Keys</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">OpenAI API Key</label>
                                <input type="password" id="openaiKey" placeholder="sk-..." 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Anthropic API Key</label>
                                <input type="password" id="anthropicKey" placeholder="sk-ant-..." 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Preferences</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Default Text Model</label>
                                <select id="defaultTextModel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                    <option value="gpt-4">GPT-4</option>
                                    <option value="claude-3-haiku">Claude 3 Haiku</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Temperature</label>
                                <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7" 
                                       class="w-full">
                                <span id="temperatureValue" class="text-sm text-gray-600">0.7</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex space-x-4">
                    <button onclick="saveConfig()" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-colors">
                        Save Configuration
                    </button>
                    <button onclick="loadConfig()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                        Load Configuration
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg p-6 flex items-center">
            <i class="fas fa-spinner fa-spin text-blue-500 text-2xl mr-3"></i>
            <span>Processing...</span>
        </div>
    </div>

    <script>
        // Global variables
        let currentChatSession = null;

        // Utility functions
        function showSection(sectionId) {
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });
            document.getElementById(sectionId).classList.remove('hidden');
        }

        function showLoading() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        function showNotification(message, type = 'info') {
            // Simple notification system
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
                type === 'error' ? 'bg-red-500' :
                type === 'success' ? 'bg-green-500' : 'bg-blue-500'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // API functions
        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(`/api${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'API request failed');
                }

                return data;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }

        // Chat functions
        async function startChatSession() {
            if (currentChatSession) return currentChatSession;

            try {
                const response = await apiCall('/chat/start', {
                    method: 'POST',
                    body: JSON.stringify({
                        model: 'gpt-3.5-turbo',
                        systemPrompt: 'You are a helpful AI assistant.'
                    })
                });

                currentChatSession = response.data.sessionId;
                return currentChatSession;
            } catch (error) {
                showNotification('Failed to start chat session: ' + error.message, 'error');
                throw error;
            }
        }

        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message) return;

            try {
                showLoading();

                // Ensure chat session exists
                await startChatSession();

                // Add user message to chat
                addMessageToChat('user', message);
                input.value = '';

                // Send to API
                const response = await apiCall('/chat/message', {
                    method: 'POST',
                    body: JSON.stringify({
                        sessionId: currentChatSession,
                        message: message
                    })
                });

                // Add AI response to chat
                addMessageToChat('assistant', response.data.message);

            } catch (error) {
                showNotification('Failed to send message: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        function addMessageToChat(role, content) {
            const messagesContainer = document.getElementById('chatMessages');

            // Clear welcome message if it exists
            if (messagesContainer.querySelector('.text-center')) {
                messagesContainer.innerHTML = '';
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `mb-4 ${role === 'user' ? 'text-right' : 'text-left'}`;

            const messageContent = document.createElement('div');
            messageContent.className = `inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                role === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-800'
            }`;
            messageContent.textContent = content;

            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Configuration functions
        async function loadConfig() {
            try {
                showLoading();
                const response = await apiCall('/config');
                const config = response.data;

                // Populate form fields
                document.getElementById('openaiKey').value = config.apiKeys.openai || '';
                document.getElementById('anthropicKey').value = config.apiKeys.anthropic || '';
                document.getElementById('defaultTextModel').value = config.preferences.defaultTextModel || 'gpt-3.5-turbo';
                document.getElementById('temperature').value = config.preferences.temperature || 0.7;
                document.getElementById('temperatureValue').textContent = config.preferences.temperature || 0.7;

                showNotification('Configuration loaded successfully', 'success');
            } catch (error) {
                showNotification('Failed to load configuration: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        async function saveConfig() {
            try {
                showLoading();

                const config = {
                    apiKeys: {
                        openai: document.getElementById('openaiKey').value,
                        anthropic: document.getElementById('anthropicKey').value
                    },
                    preferences: {
                        defaultTextModel: document.getElementById('defaultTextModel').value,
                        temperature: parseFloat(document.getElementById('temperature').value)
                    }
                };

                await apiCall('/config', {
                    method: 'PUT',
                    body: JSON.stringify(config)
                });

                showNotification('Configuration saved successfully', 'success');
            } catch (error) {
                showNotification('Failed to save configuration: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        async function checkApiStatus() {
            try {
                showLoading();
                const response = await fetch('/health');
                const data = await response.json();

                if (data.status === 'healthy') {
                    showNotification('API is healthy and running', 'success');
                } else {
                    showNotification('API status: ' + data.status, 'error');
                }
            } catch (error) {
                showNotification('Failed to check API status: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // Tool functions (placeholders)
        function showTextTools() {
            showNotification('Text tools coming soon! Use the CLI for now.', 'info');
        }

        function showImageTools() {
            showNotification('Image tools coming soon! Use the CLI for now.', 'info');
        }

        function showCodeTools() {
            showNotification('Code tools coming soon! Use the CLI for now.', 'info');
        }

        function showDocumentTools() {
            showNotification('Document tools coming soon! Use the CLI for now.', 'info');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Temperature slider
            const temperatureSlider = document.getElementById('temperature');
            const temperatureValue = document.getElementById('temperatureValue');

            temperatureSlider.addEventListener('input', function() {
                temperatureValue.textContent = this.value;
            });

            // Chat input enter key
            document.getElementById('chatInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // Load initial configuration
            loadConfig();
        });
    </script>
</body>
</html>
