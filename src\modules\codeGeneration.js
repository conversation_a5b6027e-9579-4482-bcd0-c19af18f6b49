import fs from 'fs/promises'
import path from 'path'
import { generateText } from './textGeneration.js'
import { config } from '../config/index.js'
import { logger } from '../utils/logger.js'
import { readFile, writeFile, getFileExtension } from '../utils/filesystem.js'

/**
 * Language-specific configurations
 */
const languageConfigs = {
  javascript: {
    extensions: ['.js', '.jsx', '.mjs'],
    commentStyle: '//',
    testFramework: 'jest'
  },
  typescript: {
    extensions: ['.ts', '.tsx'],
    commentStyle: '//',
    testFramework: 'jest'
  },
  python: {
    extensions: ['.py'],
    commentStyle: '#',
    testFramework: 'pytest'
  },
  java: {
    extensions: ['.java'],
    commentStyle: '//',
    testFramework: 'junit'
  },
  csharp: {
    extensions: ['.cs'],
    commentStyle: '//',
    testFramework: 'nunit'
  },
  go: {
    extensions: ['.go'],
    commentStyle: '//',
    testFramework: 'testing'
  },
  rust: {
    extensions: ['.rs'],
    commentStyle: '//',
    testFramework: 'cargo test'
  },
  php: {
    extensions: ['.php'],
    commentStyle: '//',
    testFramework: 'phpunit'
  }
}

/**
 * Detect programming language from file extension or content
 */
function detectLanguage (filePath, content = '') {
  const ext = getFileExtension(filePath)
  
  // Check by extension first
  for (const [lang, config] of Object.entries(languageConfigs)) {
    if (config.extensions.includes(ext)) {
      return lang
    }
  }
  
  // Fallback to content analysis
  if (content.includes('def ') || content.includes('import ')) return 'python'
  if (content.includes('function ') || content.includes('const ')) return 'javascript'
  if (content.includes('class ') && content.includes('public ')) return 'java'
  if (content.includes('package ') && content.includes('func ')) return 'go'
  
  return 'javascript' // Default fallback
}

/**
 * Generate code based on prompt
 */
export async function generateCode (prompt, options = {}) {
  const language = options.language || 'javascript'
  const systemPrompt = `You are an expert ${language} developer. Generate clean, well-documented, and efficient code. Follow best practices and include appropriate comments.`
  
  const enhancedPrompt = `Generate ${language} code for the following requirement:

${prompt}

Requirements:
- Write clean, readable code
- Include appropriate comments
- Follow ${language} best practices
- Add error handling where appropriate
- Make the code production-ready

Please provide only the code without additional explanations.`

  try {
    const result = await generateText(enhancedPrompt, {
      ...options,
      systemPrompt,
      model: options.model || config.models.code.default,
      temperature: 0.2
    })

    return {
      code: result.text,
      language,
      model: result.model,
      usage: result.usage
    }
  } catch (error) {
    logger.error('Code generation failed:', error)
    throw error
  }
}

/**
 * Review and analyze existing code
 */
export async function reviewCode (code, options = {}) {
  const language = options.language || detectLanguage('', code)
  
  const prompt = `Review the following ${language} code and provide detailed feedback:

\`\`\`${language}
${code}
\`\`\`

Please analyze:
1. Code quality and readability
2. Performance considerations
3. Security issues
4. Best practices adherence
5. Potential bugs or issues
6. Suggestions for improvement

Provide specific, actionable feedback.`

  const systemPrompt = `You are a senior code reviewer with expertise in ${language}. Provide constructive, detailed feedback focusing on code quality, security, performance, and best practices.`

  try {
    const result = await generateText(prompt, {
      ...options,
      systemPrompt,
      model: options.model || config.models.code.default,
      temperature: 0.3
    })

    return {
      review: result.text,
      language,
      model: result.model,
      usage: result.usage
    }
  } catch (error) {
    logger.error('Code review failed:', error)
    throw error
  }
}

/**
 * Generate documentation for code
 */
export async function generateDocumentation (code, options = {}) {
  const language = options.language || detectLanguage('', code)
  const docType = options.type || 'api' // api, readme, inline
  
  let prompt
  if (docType === 'inline') {
    prompt = `Add inline documentation comments to the following ${language} code:

\`\`\`${language}
${code}
\`\`\`

Add appropriate comments explaining:
- Function/method purposes
- Parameter descriptions
- Return values
- Complex logic
- Usage examples where helpful

Return the code with added comments.`
  } else if (docType === 'readme') {
    prompt = `Generate a comprehensive README.md for the following ${language} code:

\`\`\`${language}
${code}
\`\`\`

Include:
- Project description
- Installation instructions
- Usage examples
- API documentation
- Contributing guidelines
- License information`
  } else {
    prompt = `Generate API documentation for the following ${language} code:

\`\`\`${language}
${code}
\`\`\`

Document all public functions, classes, and methods including:
- Purpose and functionality
- Parameters with types
- Return values
- Usage examples
- Error conditions`
  }

  const systemPrompt = `You are a technical writer specializing in ${language} documentation. Create clear, comprehensive documentation that helps developers understand and use the code effectively.`

  try {
    const result = await generateText(prompt, {
      ...options,
      systemPrompt,
      model: options.model || config.models.code.default,
      temperature: 0.2
    })

    return {
      documentation: result.text,
      type: docType,
      language,
      model: result.model,
      usage: result.usage
    }
  } catch (error) {
    logger.error('Documentation generation failed:', error)
    throw error
  }
}

/**
 * Generate unit tests for code
 */
export async function generateTests (code, options = {}) {
  const language = options.language || detectLanguage('', code)
  const langConfig = languageConfigs[language] || languageConfigs.javascript
  
  const prompt = `Generate comprehensive unit tests for the following ${language} code using ${langConfig.testFramework}:

\`\`\`${language}
${code}
\`\`\`

Create tests that:
- Cover all public functions/methods
- Test edge cases and error conditions
- Include positive and negative test cases
- Follow ${langConfig.testFramework} best practices
- Have descriptive test names
- Include setup and teardown if needed

Provide complete, runnable test code.`

  const systemPrompt = `You are a test automation expert specializing in ${language} and ${langConfig.testFramework}. Write thorough, maintainable tests that ensure code reliability.`

  try {
    const result = await generateText(prompt, {
      ...options,
      systemPrompt,
      model: options.model || config.models.code.default,
      temperature: 0.2
    })

    return {
      tests: result.text,
      framework: langConfig.testFramework,
      language,
      model: result.model,
      usage: result.usage
    }
  } catch (error) {
    logger.error('Test generation failed:', error)
    throw error
  }
}

/**
 * Refactor code for better quality
 */
export async function refactorCode (code, options = {}) {
  const language = options.language || detectLanguage('', code)
  const focus = options.focus || 'general' // general, performance, readability, security
  
  const prompt = `Refactor the following ${language} code with focus on ${focus}:

\`\`\`${language}
${code}
\`\`\`

Improvements to make:
${focus === 'performance' ? '- Optimize for better performance\n- Reduce time/space complexity\n- Eliminate bottlenecks' : ''}
${focus === 'readability' ? '- Improve code readability\n- Better variable names\n- Cleaner structure' : ''}
${focus === 'security' ? '- Fix security vulnerabilities\n- Add input validation\n- Secure coding practices' : ''}
${focus === 'general' ? '- Overall code quality\n- Best practices\n- Maintainability' : ''}

Provide the refactored code with explanations of changes made.`

  const systemPrompt = `You are a senior software engineer expert in ${language}. Refactor code to improve quality while maintaining functionality.`

  try {
    const result = await generateText(prompt, {
      ...options,
      systemPrompt,
      model: options.model || config.models.code.default,
      temperature: 0.2
    })

    return {
      refactoredCode: result.text,
      focus,
      language,
      model: result.model,
      usage: result.usage
    }
  } catch (error) {
    logger.error('Code refactoring failed:', error)
    throw error
  }
}

/**
 * Main code processing function
 */
export async function processCode (input, options = {}) {
  const action = options.action || 'generate'
  
  try {
    // If input is a file path, read the file
    let code = input
    let language = options.language
    
    if (input.includes('.') && !input.includes('\n')) {
      // Likely a file path
      try {
        code = await readFile(input)
        language = language || detectLanguage(input, code)
      } catch (error) {
        // If file doesn't exist, treat as prompt
        logger.debug('File not found, treating input as prompt')
      }
    }

    let result
    switch (action) {
      case 'generate':
        result = await generateCode(input, { ...options, language })
        return result.code
      case 'review':
        result = await reviewCode(code, { ...options, language })
        return result.review
      case 'document':
        result = await generateDocumentation(code, { ...options, language })
        return result.documentation
      case 'test':
        result = await generateTests(code, { ...options, language })
        return result.tests
      case 'refactor':
        result = await refactorCode(code, { ...options, language })
        return result.refactoredCode
      default:
        throw new Error(`Unsupported action: ${action}`)
    }
  } catch (error) {
    logger.error(`Code processing failed for action ${action}:`, error)
    throw error
  }
}

/**
 * Get supported languages
 */
export function getSupportedLanguages () {
  return Object.keys(languageConfigs)
}
