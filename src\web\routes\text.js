import express from 'express'
import { generateText, generateTextVariations, summarizeText, translateText, rewriteText, getAvailableModels } from '../../modules/textGeneration.js'
import { logger } from '../../utils/logger.js'

const router = express.Router()

/**
 * Generate text
 * POST /api/text/generate
 */
router.post('/generate', async (req, res) => {
  try {
    const { prompt, model, temperature, maxLength, systemPrompt } = req.body

    if (!prompt) {
      return res.status(400).json({
        error: 'Prompt is required'
      })
    }

    const result = await generateText(prompt, {
      model,
      temperature: parseFloat(temperature),
      maxLength: parseInt(maxLength),
      systemPrompt
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Text generation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Generate text variations
 * POST /api/text/variations
 */
router.post('/variations', async (req, res) => {
  try {
    const { prompt, count, model, temperature } = req.body

    if (!prompt) {
      return res.status(400).json({
        error: 'Prompt is required'
      })
    }

    const result = await generateTextVariations(prompt, {
      count: parseInt(count) || 3,
      model,
      temperature: parseFloat(temperature)
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Text variations API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Summarize text
 * POST /api/text/summarize
 */
router.post('/summarize', async (req, res) => {
  try {
    const { text, length, model } = req.body

    if (!text) {
      return res.status(400).json({
        error: 'Text is required'
      })
    }

    const result = await summarizeText(text, {
      length,
      model
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Text summarization API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Translate text
 * POST /api/text/translate
 */
router.post('/translate', async (req, res) => {
  try {
    const { text, targetLanguage, model } = req.body

    if (!text || !targetLanguage) {
      return res.status(400).json({
        error: 'Text and target language are required'
      })
    }

    const result = await translateText(text, targetLanguage, {
      model
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Text translation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Rewrite text
 * POST /api/text/rewrite
 */
router.post('/rewrite', async (req, res) => {
  try {
    const { text, style, model } = req.body

    if (!text || !style) {
      return res.status(400).json({
        error: 'Text and style are required'
      })
    }

    const result = await rewriteText(text, style, {
      model
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Text rewriting API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Get available models
 * GET /api/text/models
 */
router.get('/models', async (req, res) => {
  try {
    const models = getAvailableModels()
    res.json({
      success: true,
      data: models
    })
  } catch (error) {
    logger.error('Get models API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

export default router
