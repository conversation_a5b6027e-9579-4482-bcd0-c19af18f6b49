# Gemini CLI - Comprehensive AI Toolbox

A powerful, extensible command-line interface and web application for interacting with multiple AI services including OpenAI, Anthropic, and local models. Built with Node.js and designed for developers, researchers, and AI enthusiasts.

## 🚀 Features

### Core AI Capabilities
- **Text Generation**: Generate, summarize, translate, and rewrite text using GPT, Claude, or local models
- **Image Generation**: Create images with DALL-E, Stable Diffusion, and other image models
- **Code Generation**: Generate, review, document, and test code in multiple programming languages
- **Document Processing**: Extract, summarize, and analyze content from PDFs, Word docs, and more
- **Interactive Chat**: Conversational AI with history management and session persistence

### Interfaces
- **CLI Interface**: Comprehensive command-line tools with intuitive subcommands
- **Web Interface**: Modern web application with REST API
- **Programmatic API**: Use as a Node.js library in your own projects

### Advanced Features
- **Multi-Provider Support**: OpenAI, Anthropic, Hugging Face, Stability AI, and local models
- **Configuration Management**: Flexible settings with environment variables and user preferences
- **File Upload Support**: Process documents, images, and code files
- **Conversation History**: Save and manage chat sessions
- **Rate Limiting**: Built-in protection against API abuse
- **Comprehensive Logging**: Detailed logging with configurable levels
- **Docker Support**: Easy deployment with containerization

## 📦 Installation

### Prerequisites
- Node.js 18.0.0 or higher
- npm or yarn package manager

### Quick Start

```bash
# Clone the repository
git clone https://github.com/yourusername/gemini-cli.git
cd gemini-cli

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Edit .env file with your API keys
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Run the application
npm start

# Or use the CLI directly
npm run cli -- --help
```

### Global Installation

```bash
# Install globally for system-wide access
npm install -g .

# Use from anywhere
gemini --help
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root:

```env
# API Keys (at least one required)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
STABILITY_API_KEY=your_stability_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development

# Model Defaults
DEFAULT_TEXT_MODEL=gpt-3.5-turbo
DEFAULT_IMAGE_MODEL=dall-e-3
DEFAULT_CODE_MODEL=gpt-4

# Feature Flags
ENABLE_WEB_INTERFACE=true
ENABLE_FILE_UPLOAD=true
ENABLE_CONVERSATION_HISTORY=true
```

### User Configuration

Use the CLI to manage user preferences:

```bash
# Show current configuration
gemini config --show

# Set a configuration value
gemini config --set preferences.temperature=0.8

# Reset to defaults
gemini config --reset
```

## 💻 CLI Usage

### Text Generation

```bash
# Generate text
gemini text "Write a short story about AI" --model gpt-4 --temperature 0.7

# Summarize text
gemini text "Long text to summarize..." --action summarize --length short

# Translate text
gemini text "Hello world" --action translate --target-language Spanish

# Save output to file
gemini text "Generate documentation" --output result.txt
```

### Image Generation

```bash
# Generate image
gemini image "A futuristic city at sunset" --model dall-e-3 --size 1024x1024

# Generate multiple images
gemini image "Abstract art" --number 3 --output ./images

# Use different providers
gemini image "Mountain landscape" --model stable-diffusion-xl
```

### Code Generation

```bash
# Generate code
gemini code "Create a REST API in Node.js" --language javascript

# Review existing code
gemini code ./src/app.js --action review

# Generate documentation
gemini code ./src/utils.js --action document --type api

# Generate tests
gemini code ./src/calculator.js --action test
```

### Document Processing

```bash
# Summarize document
gemini document ./report.pdf --action summarize --length medium

# Extract key information
gemini document ./contract.docx --action key-info --type entities

# Translate document
gemini document ./article.txt --action translate --language French
```

### Interactive Chat

```bash
# Start chat session
gemini chat --model gpt-4 --history

# Chat with system prompt
gemini chat --system "You are a coding assistant" --model claude-3-sonnet
```

### Web Server

```bash
# Start web interface
gemini serve --port 3000

# Start with custom host
gemini serve --host 0.0.0.0 --port 8080
```

## 🌐 Web Interface

Access the web interface at `http://localhost:3000` after starting the server.

### Features
- **Dashboard**: Overview of all AI tools
- **Interactive Chat**: Web-based chat interface
- **File Upload**: Process documents and images
- **Configuration**: Manage API keys and preferences
- **API Status**: Monitor service health

### API Endpoints

#### Text Generation
- `POST /api/text/generate` - Generate text
- `POST /api/text/summarize` - Summarize text
- `POST /api/text/translate` - Translate text
- `GET /api/text/models` - Get available models

#### Image Generation
- `POST /api/image/generate` - Generate images
- `POST /api/image/variations` - Generate variations
- `POST /api/image/edit` - Edit images (DALL-E only)

#### Code Processing
- `POST /api/code/generate` - Generate code
- `POST /api/code/review` - Review code
- `POST /api/code/document` - Generate documentation
- `POST /api/code/test` - Generate tests

#### Document Processing
- `POST /api/document/upload` - Upload and process document
- `POST /api/document/summarize` - Summarize text
- `POST /api/document/extract` - Extract information

#### Chat
- `POST /api/chat/start` - Start chat session
- `POST /api/chat/message` - Send message
- `GET /api/chat/:id/history` - Get chat history

## 🧪 Testing

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test file
npm test -- textGeneration.test.js

# Watch mode for development
npm run test:watch
```

## 🐳 Docker Deployment

```bash
# Build Docker image
npm run docker:build

# Run container
npm run docker:run

# Or use docker-compose
docker-compose up -d
```

### Docker Compose

```yaml
version: '3.8'
services:
  gemini-cli:
    build: .
    ports:
      - "3000:3000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
```

## 📚 Examples

See the `examples/` directory for detailed usage examples:

- `text-generation.js` - Text generation examples
- `image-creation.js` - Image generation workflows
- `code-assistant.js` - Code generation and review
- `document-analysis.js` - Document processing examples
- `chat-bot.js` - Building chat applications

## 🔌 Programmatic Usage

Use Gemini CLI as a library in your Node.js projects:

```javascript
import { generateText, generateImage, processCode } from 'gemini-cli'

// Generate text
const textResult = await generateText('Write a haiku about programming', {
  model: 'gpt-4',
  temperature: 0.8
})

// Generate image
const imageResult = await generateImage('A serene mountain lake', {
  model: 'dall-e-3',
  size: '1024x1024'
})

// Process code
const codeReview = await processCode('./src/app.js', {
  action: 'review',
  language: 'javascript'
})
```

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Development Setup

```bash
# Clone and install
git clone https://github.com/yourusername/gemini-cli.git
cd gemini-cli
npm install

# Run in development mode
npm run dev

# Run tests
npm test

# Lint code
npm run lint
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT models and DALL-E
- Anthropic for Claude models
- Stability AI for Stable Diffusion
- The open-source community for various tools and libraries

## 📞 Support

- 📖 [Documentation](docs/)
- 🐛 [Issue Tracker](https://github.com/yourusername/gemini-cli/issues)
- 💬 [Discussions](https://github.com/yourusername/gemini-cli/discussions)
- 📧 [Email Support](mailto:<EMAIL>)

---

**Made with ❤️ by the Gemini CLI team**
