import inquirer from 'inquirer'
import chalk from 'chalk'
import fs from 'fs/promises'
import path from 'path'
import { generateText } from './textGeneration.js'
import { config } from '../config/index.js'
import { logger } from '../utils/logger.js'
import { getMergedConfig } from '../utils/configManager.js'

/**
 * Chat session class
 */
class ChatSession {
  constructor (options = {}) {
    this.model = options.model || config.models.chat.default
    this.systemPrompt = options.system || 'You are a helpful AI assistant.'
    this.history = []
    this.maxHistory = options.maxHistory || config.models.chat.maxHistory
    this.saveHistory = options.history !== false
    this.sessionId = `chat_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
    this.startTime = new Date()
  }

  /**
   * Add message to history
   */
  addToHistory (role, content) {
    this.history.push({
      role,
      content,
      timestamp: new Date().toISOString()
    })

    // Trim history if too long
    if (this.history.length > this.maxHistory * 2) {
      this.history = this.history.slice(-this.maxHistory * 2)
    }
  }

  /**
   * Get conversation context for AI
   */
  getContext () {
    const messages = [
      {
        role: 'system',
        content: this.systemPrompt
      }
    ]

    // Add recent history
    const recentHistory = this.history.slice(-this.maxHistory)
    messages.push(...recentHistory.map(msg => ({
      role: msg.role,
      content: msg.content
    })))

    return messages
  }

  /**
   * Send message and get response
   */
  async sendMessage (userMessage) {
    this.addToHistory('user', userMessage)

    try {
      const context = this.getContext()
      const prompt = context.map(msg => `${msg.role}: ${msg.content}`).join('\n\n')

      const result = await generateText(prompt, {
        model: this.model,
        temperature: 0.7,
        maxLength: 2000
      })

      const assistantMessage = result.text
      this.addToHistory('assistant', assistantMessage)

      return {
        message: assistantMessage,
        model: result.model,
        usage: result.usage
      }
    } catch (error) {
      logger.error('Chat message failed:', error)
      throw error
    }
  }

  /**
   * Save conversation history
   */
  async saveConversation () {
    if (!this.saveHistory || this.history.length === 0) {
      return
    }

    try {
      const historyDir = path.join(config.paths.data, 'conversations')
      await fs.mkdir(historyDir, { recursive: true })

      const filename = `${this.sessionId}.json`
      const filePath = path.join(historyDir, filename)

      const conversationData = {
        sessionId: this.sessionId,
        model: this.model,
        systemPrompt: this.systemPrompt,
        startTime: this.startTime,
        endTime: new Date(),
        messageCount: this.history.length,
        history: this.history
      }

      await fs.writeFile(filePath, JSON.stringify(conversationData, null, 2))
      logger.info(`Conversation saved: ${filePath}`)
    } catch (error) {
      logger.error('Failed to save conversation:', error)
    }
  }

  /**
   * Load conversation history
   */
  static async loadConversation (sessionId) {
    try {
      const historyDir = path.join(config.paths.data, 'conversations')
      const filePath = path.join(historyDir, `${sessionId}.json`)
      
      const data = await fs.readFile(filePath, 'utf8')
      const conversationData = JSON.parse(data)
      
      const session = new ChatSession({
        model: conversationData.model,
        system: conversationData.systemPrompt
      })
      
      session.sessionId = conversationData.sessionId
      session.history = conversationData.history || []
      session.startTime = new Date(conversationData.startTime)
      
      return session
    } catch (error) {
      logger.error(`Failed to load conversation ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Get conversation summary
   */
  getSummary () {
    const messageCount = this.history.length
    const userMessages = this.history.filter(msg => msg.role === 'user').length
    const assistantMessages = this.history.filter(msg => msg.role === 'assistant').length
    const duration = new Date() - this.startTime

    return {
      sessionId: this.sessionId,
      model: this.model,
      messageCount,
      userMessages,
      assistantMessages,
      duration: Math.round(duration / 1000), // seconds
      startTime: this.startTime
    }
  }
}

/**
 * Start interactive CLI chat session
 */
export async function startChat (options = {}) {
  console.log(chalk.blue('🤖 Welcome to Gemini Chat!'))
  console.log(chalk.gray('Type "exit" to quit, "clear" to clear history, "save" to save conversation'))
  console.log(chalk.gray('─'.repeat(60)))

  const session = new ChatSession(options)
  
  console.log(chalk.cyan(`Model: ${session.model}`))
  console.log(chalk.cyan(`Session ID: ${session.sessionId}`))
  console.log()

  try {
    while (true) {
      const { message } = await inquirer.prompt([
        {
          type: 'input',
          name: 'message',
          message: chalk.green('You:'),
          validate: (input) => {
            if (!input.trim()) {
              return 'Please enter a message'
            }
            return true
          }
        }
      ])

      const userInput = message.trim()

      // Handle special commands
      if (userInput.toLowerCase() === 'exit') {
        break
      }

      if (userInput.toLowerCase() === 'clear') {
        session.history = []
        console.log(chalk.yellow('💭 Conversation history cleared'))
        continue
      }

      if (userInput.toLowerCase() === 'save') {
        await session.saveConversation()
        console.log(chalk.green('💾 Conversation saved'))
        continue
      }

      if (userInput.toLowerCase() === 'summary') {
        const summary = session.getSummary()
        console.log(chalk.blue('\n📊 Conversation Summary:'))
        console.log(chalk.white(`  Messages: ${summary.messageCount}`))
        console.log(chalk.white(`  Duration: ${summary.duration}s`))
        console.log(chalk.white(`  Model: ${summary.model}`))
        console.log()
        continue
      }

      // Send message to AI
      try {
        console.log(chalk.yellow('🤔 Thinking...'))
        
        const response = await session.sendMessage(userInput)
        
        console.log(chalk.blue('\nAI:'))
        console.log(chalk.white(response.message))
        console.log(chalk.gray(`\n(Model: ${response.model}, Tokens: ${response.usage?.total_tokens || 'N/A'})`))
        console.log()
      } catch (error) {
        console.error(chalk.red('❌ Error:'), error.message)
        console.log()
      }
    }
  } catch (error) {
    if (error.isTtyError) {
      console.error(chalk.red('❌ Interactive terminal required'))
    } else {
      console.error(chalk.red('❌ Chat error:'), error.message)
    }
  } finally {
    // Save conversation on exit
    if (session.history.length > 0) {
      await session.saveConversation()
      const summary = session.getSummary()
      console.log(chalk.blue('\n👋 Chat session ended'))
      console.log(chalk.gray(`Messages: ${summary.messageCount}, Duration: ${summary.duration}s`))
    }
  }
}

/**
 * List saved conversations
 */
export async function listConversations () {
  try {
    const historyDir = path.join(config.paths.data, 'conversations')
    const files = await fs.readdir(historyDir)
    const conversations = []

    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(historyDir, file)
          const data = await fs.readFile(filePath, 'utf8')
          const conversationData = JSON.parse(data)
          
          conversations.push({
            sessionId: conversationData.sessionId,
            model: conversationData.model,
            messageCount: conversationData.messageCount || 0,
            startTime: new Date(conversationData.startTime),
            endTime: conversationData.endTime ? new Date(conversationData.endTime) : null
          })
        } catch (error) {
          logger.warn(`Failed to parse conversation file ${file}:`, error.message)
        }
      }
    }

    return conversations.sort((a, b) => b.startTime - a.startTime)
  } catch (error) {
    logger.error('Failed to list conversations:', error)
    return []
  }
}

/**
 * Delete conversation
 */
export async function deleteConversation (sessionId) {
  try {
    const historyDir = path.join(config.paths.data, 'conversations')
    const filePath = path.join(historyDir, `${sessionId}.json`)
    
    await fs.unlink(filePath)
    logger.info(`Conversation deleted: ${sessionId}`)
  } catch (error) {
    logger.error(`Failed to delete conversation ${sessionId}:`, error)
    throw error
  }
}

/**
 * Export conversation to different formats
 */
export async function exportConversation (sessionId, format = 'json') {
  try {
    const session = await ChatSession.loadConversation(sessionId)
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    
    let content
    let extension
    
    switch (format.toLowerCase()) {
      case 'txt':
        content = session.history.map(msg => 
          `[${msg.timestamp}] ${msg.role.toUpperCase()}: ${msg.content}`
        ).join('\n\n')
        extension = 'txt'
        break
      case 'md':
        content = `# Chat Session: ${sessionId}\n\n`
        content += `**Model:** ${session.model}\n`
        content += `**Start Time:** ${session.startTime}\n\n`
        content += session.history.map(msg => 
          `## ${msg.role.charAt(0).toUpperCase() + msg.role.slice(1)}\n\n${msg.content}\n`
        ).join('\n')
        extension = 'md'
        break
      default:
        content = JSON.stringify({
          sessionId: session.sessionId,
          model: session.model,
          systemPrompt: session.systemPrompt,
          startTime: session.startTime,
          history: session.history
        }, null, 2)
        extension = 'json'
    }
    
    const filename = `chat_export_${sessionId}_${timestamp}.${extension}`
    const exportPath = path.join(config.paths.data, 'exports', filename)
    
    await fs.mkdir(path.dirname(exportPath), { recursive: true })
    await fs.writeFile(exportPath, content)
    
    return exportPath
  } catch (error) {
    logger.error(`Failed to export conversation ${sessionId}:`, error)
    throw error
  }
}

export { ChatSession }
