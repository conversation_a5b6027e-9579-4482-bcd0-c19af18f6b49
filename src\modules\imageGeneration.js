import OpenAI from 'openai'
import axios from 'axios'
import fs from 'fs/promises'
import path from 'path'
import { config } from '../config/index.js'
import { logger, logApiCall, logError } from '../utils/logger.js'
import { generateUniqueFilename, createDirectories } from '../utils/filesystem.js'

/**
 * Initialize OpenAI client
 */
let openaiClient = null

function initializeOpenAI () {
  if (config.apiKeys.openai && !openaiClient) {
    openaiClient = new OpenAI({
      apiKey: config.apiKeys.openai
    })
  }
}

/**
 * Generate image using DALL-E
 */
async function generateWithDALLE (prompt, options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI API key not configured')
  }

  const startTime = Date.now()
  
  try {
    const response = await openaiClient.images.generate({
      model: options.model || 'dall-e-3',
      prompt: prompt,
      n: parseInt(options.number) || 1,
      size: options.size || '1024x1024',
      quality: options.quality || 'standard',
      style: options.style || 'vivid',
      response_format: 'url'
    })

    const duration = Date.now() - startTime
    const images = []

    // Download and save images
    for (let i = 0; i < response.data.length; i++) {
      const imageData = response.data[i]
      const filename = generateUniqueFilename('dalle_image', '.png')
      const outputPath = path.join(options.output || './images', filename)
      
      // Download image
      const imageResponse = await axios.get(imageData.url, {
        responseType: 'arraybuffer'
      })
      
      // Ensure output directory exists
      await fs.mkdir(path.dirname(outputPath), { recursive: true })
      
      // Save image
      await fs.writeFile(outputPath, imageResponse.data)
      
      images.push({
        path: outputPath,
        url: imageData.url,
        revisedPrompt: imageData.revised_prompt,
        filename
      })
    }

    logApiCall('openai-dalle', options.model, prompt, `${images.length} images`, duration)

    return {
      images,
      model: options.model,
      originalPrompt: prompt,
      duration
    }
  } catch (error) {
    logError(error, { provider: 'openai-dalle', model: options.model })
    throw new Error(`DALL-E API error: ${error.message}`)
  }
}

/**
 * Generate image using Stability AI
 */
async function generateWithStability (prompt, options = {}) {
  if (!config.apiKeys.stability) {
    throw new Error('Stability AI API key not configured')
  }

  const startTime = Date.now()
  
  try {
    const response = await axios.post(
      'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image',
      {
        text_prompts: [
          {
            text: prompt,
            weight: 1
          }
        ],
        cfg_scale: options.cfgScale || 7,
        height: parseInt(options.height) || 1024,
        width: parseInt(options.width) || 1024,
        samples: parseInt(options.number) || 1,
        steps: parseInt(options.steps) || 30,
        style_preset: options.stylePreset || 'enhance'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${config.apiKeys.stability}`
        }
      }
    )

    const duration = Date.now() - startTime
    const images = []

    // Save images
    for (let i = 0; i < response.data.artifacts.length; i++) {
      const artifact = response.data.artifacts[i]
      const filename = generateUniqueFilename('stability_image', '.png')
      const outputPath = path.join(options.output || './images', filename)
      
      // Ensure output directory exists
      await fs.mkdir(path.dirname(outputPath), { recursive: true })
      
      // Save base64 image
      const imageBuffer = Buffer.from(artifact.base64, 'base64')
      await fs.writeFile(outputPath, imageBuffer)
      
      images.push({
        path: outputPath,
        filename,
        seed: artifact.seed,
        finishReason: artifact.finishReason
      })
    }

    logApiCall('stability', 'stable-diffusion-xl', prompt, `${images.length} images`, duration)

    return {
      images,
      model: 'stable-diffusion-xl-1024-v1-0',
      originalPrompt: prompt,
      duration
    }
  } catch (error) {
    logError(error, { provider: 'stability' })
    throw new Error(`Stability AI error: ${error.response?.data?.message || error.message}`)
  }
}

/**
 * Generate image using Hugging Face
 */
async function generateWithHuggingFace (prompt, options = {}) {
  if (!config.apiKeys.huggingface) {
    throw new Error('Hugging Face API key not configured')
  }

  const startTime = Date.now()
  const model = options.model || 'stabilityai/stable-diffusion-2-1'
  
  try {
    const response = await axios.post(
      `https://api-inference.huggingface.co/models/${model}`,
      {
        inputs: prompt,
        parameters: {
          num_inference_steps: parseInt(options.steps) || 20,
          guidance_scale: parseFloat(options.guidanceScale) || 7.5,
          width: parseInt(options.width) || 512,
          height: parseInt(options.height) || 512
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${config.apiKeys.huggingface}`,
          'Content-Type': 'application/json'
        },
        responseType: 'arraybuffer'
      }
    )

    const duration = Date.now() - startTime
    const filename = generateUniqueFilename('hf_image', '.png')
    const outputPath = path.join(options.output || './images', filename)
    
    // Ensure output directory exists
    await fs.mkdir(path.dirname(outputPath), { recursive: true })
    
    // Save image
    await fs.writeFile(outputPath, response.data)

    const images = [{
      path: outputPath,
      filename
    }]

    logApiCall('huggingface', model, prompt, '1 image', duration)

    return {
      images,
      model,
      originalPrompt: prompt,
      duration
    }
  } catch (error) {
    logError(error, { provider: 'huggingface', model: options.model })
    throw new Error(`Hugging Face error: ${error.message}`)
  }
}

/**
 * Determine provider from model name
 */
function getProviderFromModel (model) {
  if (model.startsWith('dall-e')) {
    return 'openai'
  } else if (model.includes('stable-diffusion')) {
    return 'stability'
  } else {
    return 'huggingface'
  }
}

/**
 * Main image generation function
 */
export async function generateImage (prompt, options = {}) {
  if (!prompt || typeof prompt !== 'string') {
    throw new Error('Prompt is required and must be a string')
  }

  // Initialize clients
  initializeOpenAI()

  // Ensure output directory exists
  await createDirectories()

  // Determine model and provider
  const model = options.model || config.models.image.default
  const provider = options.provider || getProviderFromModel(model)

  logger.info(`Generating image with ${provider}:${model}`)

  // Generate based on provider
  let result
  switch (provider) {
    case 'openai':
      result = await generateWithDALLE(prompt, { ...options, model })
      break
    case 'stability':
      result = await generateWithStability(prompt, { ...options, model })
      break
    case 'huggingface':
      result = await generateWithHuggingFace(prompt, { ...options, model })
      break
    default:
      throw new Error(`Unsupported provider: ${provider}`)
  }

  return result
}

/**
 * Generate image variations
 */
export async function generateImageVariations (prompt, options = {}) {
  const count = options.count || 3
  const variations = []

  for (let i = 0; i < count; i++) {
    try {
      // Modify prompt slightly for variations
      const modifiedPrompt = `${prompt}, variation ${i + 1}`
      const variation = await generateImage(modifiedPrompt, {
        ...options,
        number: 1
      })
      variations.push(variation)
    } catch (error) {
      logger.warn(`Failed to generate variation ${i + 1}:`, error.message)
    }
  }

  return variations
}

/**
 * Edit image (DALL-E only)
 */
export async function editImage (imagePath, maskPath, prompt, options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI API key not configured')
  }

  const startTime = Date.now()
  
  try {
    const response = await openaiClient.images.edit({
      model: 'dall-e-2', // Only DALL-E 2 supports editing
      image: fs.createReadStream(imagePath),
      mask: maskPath ? fs.createReadStream(maskPath) : undefined,
      prompt: prompt,
      n: parseInt(options.number) || 1,
      size: options.size || '1024x1024',
      response_format: 'url'
    })

    const duration = Date.now() - startTime
    const images = []

    // Download and save edited images
    for (let i = 0; i < response.data.length; i++) {
      const imageData = response.data[i]
      const filename = generateUniqueFilename('dalle_edit', '.png')
      const outputPath = path.join(options.output || './images', filename)
      
      // Download image
      const imageResponse = await axios.get(imageData.url, {
        responseType: 'arraybuffer'
      })
      
      // Ensure output directory exists
      await fs.mkdir(path.dirname(outputPath), { recursive: true })
      
      // Save image
      await fs.writeFile(outputPath, imageResponse.data)
      
      images.push({
        path: outputPath,
        url: imageData.url,
        filename
      })
    }

    logApiCall('openai-dalle-edit', 'dall-e-2', prompt, `${images.length} images`, duration)

    return {
      images,
      model: 'dall-e-2',
      originalPrompt: prompt,
      duration
    }
  } catch (error) {
    logError(error, { provider: 'openai-dalle-edit' })
    throw new Error(`DALL-E edit error: ${error.message}`)
  }
}

/**
 * Get available models for each provider
 */
export function getAvailableImageModels () {
  return {
    openai: ['dall-e-2', 'dall-e-3'],
    stability: ['stable-diffusion-xl-1024-v1-0'],
    huggingface: [
      'stabilityai/stable-diffusion-2-1',
      'runwayml/stable-diffusion-v1-5',
      'CompVis/stable-diffusion-v1-4'
    ]
  }
}
