import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import multer from 'multer'
import path from 'path'
import { fileURLToPath } from 'url'
import { config } from '../config/index.js'
import { logger } from '../utils/logger.js'
import { createDirectories } from '../utils/filesystem.js'

// Import route handlers
import textRoutes from './routes/text.js'
import imageRoutes from './routes/image.js'
import codeRoutes from './routes/code.js'
import documentRoutes from './routes/document.js'
import chatRoutes from './routes/chat.js'
import configRoutes from './routes/config.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * Create Express application
 */
function createApp () {
  const app = express()

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
        scriptSrc: ["'self'", "https://cdnjs.cloudflare.com"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"]
      }
    }
  }))

  // CORS configuration
  app.use(cors({
    origin: config.server.corsOrigin,
    credentials: true
  }))

  // Logging middleware
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }))

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }))
  app.use(express.urlencoded({ extended: true, limit: '10mb' }))

  // File upload configuration
  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, config.paths.uploads)
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
      cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
    }
  })

  const upload = multer({
    storage,
    limits: {
      fileSize: config.upload.maxFileSize
    },
    fileFilter: (req, file, cb) => {
      const allowedTypes = config.upload.allowedTypes
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true)
      } else {
        cb(new Error(`File type ${file.mimetype} not allowed`), false)
      }
    }
  })

  // Make upload middleware available to routes
  app.locals.upload = upload

  // Serve static files
  app.use('/static', express.static(path.join(__dirname, 'public')))
  app.use('/uploads', express.static(config.paths.uploads))

  // API routes
  app.use('/api/text', textRoutes)
  app.use('/api/image', imageRoutes)
  app.use('/api/code', codeRoutes)
  app.use('/api/document', documentRoutes)
  app.use('/api/chat', chatRoutes)
  app.use('/api/config', configRoutes)

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime()
    })
  })

  // API info endpoint
  app.get('/api', (req, res) => {
    res.json({
      name: 'Gemini CLI API',
      version: '1.0.0',
      description: 'Comprehensive generative AI toolbox API',
      endpoints: {
        text: '/api/text',
        image: '/api/image',
        code: '/api/code',
        document: '/api/document',
        chat: '/api/chat',
        config: '/api/config'
      },
      documentation: '/api/docs'
    })
  })

  // Serve main web interface
  app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'))
  })

  // Error handling middleware
  app.use((error, req, res, next) => {
    logger.error('Express error:', error)

    if (error instanceof multer.MulterError) {
      if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          error: 'File too large',
          message: `Maximum file size is ${config.upload.maxFileSize} bytes`
        })
      }
    }

    res.status(error.status || 500).json({
      error: error.message || 'Internal server error',
      ...(config.isDevelopment && { stack: error.stack })
    })
  })

  // 404 handler
  app.use((req, res) => {
    res.status(404).json({
      error: 'Not found',
      message: `Route ${req.method} ${req.path} not found`
    })
  })

  return app
}

/**
 * Start web server
 */
export async function startWebServer (options = {}) {
  try {
    // Ensure directories exist
    await createDirectories()

    // Create Express app
    const app = createApp()

    // Server configuration
    const port = options.port || config.server.port
    const host = options.host || config.server.host

    // Start server
    const server = app.listen(port, host, () => {
      logger.info(`🌐 Web server started on http://${host}:${port}`)
      logger.info(`📚 API documentation: http://${host}:${port}/api`)
      logger.info(`🏥 Health check: http://${host}:${port}/health`)
    })

    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down web server...')
      server.close(() => {
        logger.info('Web server closed')
      })
    })

    return server
  } catch (error) {
    logger.error('Failed to start web server:', error)
    throw error
  }
}

export default createApp
