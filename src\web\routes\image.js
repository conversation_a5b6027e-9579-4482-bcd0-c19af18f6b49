import express from 'express'
import { generateImage, generateImageVariations, editImage, getAvailableImageModels } from '../../modules/imageGeneration.js'
import { logger } from '../../utils/logger.js'

const router = express.Router()

/**
 * Generate image
 * POST /api/image/generate
 */
router.post('/generate', async (req, res) => {
  try {
    const { prompt, model, size, number, quality, style, output } = req.body

    if (!prompt) {
      return res.status(400).json({
        error: 'Prompt is required'
      })
    }

    const result = await generateImage(prompt, {
      model,
      size,
      number: parseInt(number) || 1,
      quality,
      style,
      output: output || './uploads/images'
    })

    // Convert file paths to URLs
    const imagesWithUrls = result.images.map(img => ({
      ...img,
      url: `/uploads/images/${img.filename}`
    }))

    res.json({
      success: true,
      data: {
        ...result,
        images: imagesWithUrls
      }
    })
  } catch (error) {
    logger.error('Image generation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Generate image variations
 * POST /api/image/variations
 */
router.post('/variations', async (req, res) => {
  try {
    const { prompt, count, model, size } = req.body

    if (!prompt) {
      return res.status(400).json({
        error: 'Prompt is required'
      })
    }

    const result = await generateImageVariations(prompt, {
      count: parseInt(count) || 3,
      model,
      size,
      output: './uploads/images'
    })

    // Convert file paths to URLs
    const variationsWithUrls = result.map(variation => ({
      ...variation,
      images: variation.images.map(img => ({
        ...img,
        url: `/uploads/images/${img.filename}`
      }))
    }))

    res.json({
      success: true,
      data: variationsWithUrls
    })
  } catch (error) {
    logger.error('Image variations API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Edit image (DALL-E only)
 * POST /api/image/edit
 */
router.post('/edit', (req, res, next) => {
  const upload = req.app.locals.upload
  upload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'mask', maxCount: 1 }
  ])(req, res, next)
}, async (req, res) => {
  try {
    const { prompt, size, number } = req.body

    if (!prompt) {
      return res.status(400).json({
        error: 'Prompt is required'
      })
    }

    if (!req.files || !req.files.image) {
      return res.status(400).json({
        error: 'Image file is required'
      })
    }

    const imagePath = req.files.image[0].path
    const maskPath = req.files.mask ? req.files.mask[0].path : null

    const result = await editImage(imagePath, maskPath, prompt, {
      size,
      number: parseInt(number) || 1,
      output: './uploads/images'
    })

    // Convert file paths to URLs
    const imagesWithUrls = result.images.map(img => ({
      ...img,
      url: `/uploads/images/${img.filename}`
    }))

    res.json({
      success: true,
      data: {
        ...result,
        images: imagesWithUrls
      }
    })
  } catch (error) {
    logger.error('Image edit API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Get available models
 * GET /api/image/models
 */
router.get('/models', async (req, res) => {
  try {
    const models = getAvailableImageModels()
    res.json({
      success: true,
      data: models
    })
  } catch (error) {
    logger.error('Get image models API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

export default router
