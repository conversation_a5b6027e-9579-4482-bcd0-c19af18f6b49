import { jest } from '@jest/globals'

// Mock environment variables for testing
process.env.NODE_ENV = 'test'
process.env.OPENAI_API_KEY = 'test-openai-key'
process.env.ANTHROPIC_API_KEY = 'test-anthropic-key'
process.env.LOG_LEVEL = 'error'

// Global test setup
beforeAll(async () => {
  // Setup test database or other resources
})

afterAll(async () => {
  // Cleanup test resources
})

beforeEach(() => {
  // Reset mocks before each test
  jest.clearAllMocks()
})

// Mock external dependencies
jest.mock('openai', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn().mockResolvedValue({
            choices: [{ message: { content: 'Mock response' } }],
            usage: { total_tokens: 100 }
          })
        }
      },
      images: {
        generate: jest.fn().mockResolvedValue({
          data: [{ url: 'https://example.com/image.png' }]
        })
      }
    }))
  }
})

jest.mock('@anthropic-ai/sdk', () => {
  return {
    default: jest.fn().mockImplementation(() => ({
      messages: {
        create: jest.fn().mockResolvedValue({
          content: [{ text: 'Mock Anthropic response' }],
          usage: { input_tokens: 50, output_tokens: 50 }
        })
      }
    }))
  }
})

// Mock file system operations
jest.mock('fs/promises', () => ({
  readFile: jest.fn(),
  writeFile: jest.fn(),
  mkdir: jest.fn(),
  access: jest.fn(),
  stat: jest.fn(),
  readdir: jest.fn(),
  unlink: jest.fn(),
  copyFile: jest.fn()
}))

// Mock axios for HTTP requests
jest.mock('axios', () => ({
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  },
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn()
}))

// Test utilities
export const mockApiResponse = (data, status = 200) => {
  return Promise.resolve({
    data,
    status,
    statusText: 'OK',
    headers: {},
    config: {}
  })
}

export const mockApiError = (message, status = 500) => {
  const error = new Error(message)
  error.response = {
    data: { error: message },
    status,
    statusText: 'Error'
  }
  return Promise.reject(error)
}

export const createMockFile = (content = 'test content', filename = 'test.txt') => {
  return {
    originalname: filename,
    filename: `mock_${filename}`,
    path: `/tmp/${filename}`,
    size: content.length,
    mimetype: 'text/plain',
    buffer: Buffer.from(content)
  }
}
