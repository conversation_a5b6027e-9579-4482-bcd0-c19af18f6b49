import express from 'express'
import { processDocument, getSupportedFormats } from '../../modules/documentProcessing.js'
import { logger } from '../../utils/logger.js'

const router = express.Router()

/**
 * Upload and process document
 * POST /api/document/upload
 */
router.post('/upload', (req, res, next) => {
  const upload = req.app.locals.upload
  upload.single('document')(req, res, next)
}, async (req, res) => {
  try {
    const { action, length, style, type, language } = req.body

    if (!req.file) {
      return res.status(400).json({
        error: 'Document file is required'
      })
    }

    const result = await processDocument(req.file.path, {
      action: action || 'summarize',
      length,
      style,
      type,
      language
    })

    res.json({
      success: true,
      data: {
        result,
        filename: req.file.originalname,
        action: action || 'summarize'
      }
    })
  } catch (error) {
    logger.error('Document processing API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Process text directly
 * POST /api/document/process
 */
router.post('/process', async (req, res) => {
  try {
    const { text, action, length, style, type, language, model } = req.body

    if (!text) {
      return res.status(400).json({
        error: 'Text is required'
      })
    }

    // Create a temporary file for processing
    const fs = await import('fs/promises')
    const path = await import('path')
    const { config } = await import('../../config/index.js')
    
    const tempFile = path.join(config.paths.temp, `temp_${Date.now()}.txt`)
    await fs.writeFile(tempFile, text)

    try {
      const result = await processDocument(tempFile, {
        action: action || 'summarize',
        length,
        style,
        type,
        language,
        model
      })

      res.json({
        success: true,
        data: {
          result,
          action: action || 'summarize'
        }
      })
    } finally {
      // Clean up temp file
      try {
        await fs.unlink(tempFile)
      } catch (error) {
        logger.warn('Failed to clean up temp file:', error.message)
      }
    }
  } catch (error) {
    logger.error('Document text processing API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Summarize document
 * POST /api/document/summarize
 */
router.post('/summarize', async (req, res) => {
  try {
    const { text, length, style, model } = req.body

    if (!text) {
      return res.status(400).json({
        error: 'Text is required'
      })
    }

    const { summarizeDocument } = await import('../../modules/documentProcessing.js')
    const result = await summarizeDocument(text, {
      length,
      style,
      model
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Document summarization API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Extract key information
 * POST /api/document/extract
 */
router.post('/extract', async (req, res) => {
  try {
    const { text, type, model } = req.body

    if (!text) {
      return res.status(400).json({
        error: 'Text is required'
      })
    }

    const { extractKeyInformation } = await import('../../modules/documentProcessing.js')
    const result = await extractKeyInformation(text, {
      type,
      model
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Information extraction API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Translate document
 * POST /api/document/translate
 */
router.post('/translate', async (req, res) => {
  try {
    const { text, targetLanguage, model } = req.body

    if (!text || !targetLanguage) {
      return res.status(400).json({
        error: 'Text and target language are required'
      })
    }

    const { translateDocument } = await import('../../modules/documentProcessing.js')
    const result = await translateDocument(text, targetLanguage, {
      model
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Document translation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Analyze sentiment
 * POST /api/document/sentiment
 */
router.post('/sentiment', async (req, res) => {
  try {
    const { text, model } = req.body

    if (!text) {
      return res.status(400).json({
        error: 'Text is required'
      })
    }

    const { analyzeDocumentSentiment } = await import('../../modules/documentProcessing.js')
    const result = await analyzeDocumentSentiment(text, {
      model
    })

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    logger.error('Sentiment analysis API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Get supported formats
 * GET /api/document/formats
 */
router.get('/formats', async (req, res) => {
  try {
    const formats = getSupportedFormats()
    res.json({
      success: true,
      data: formats
    })
  } catch (error) {
    logger.error('Get formats API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

export default router
