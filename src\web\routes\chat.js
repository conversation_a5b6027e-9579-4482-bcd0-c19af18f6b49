import express from 'express'
import { ChatSession, listConversations, deleteConversation, exportConversation } from '../../modules/chat.js'
import { logger } from '../../utils/logger.js'

const router = express.Router()

// Store active chat sessions
const activeSessions = new Map()

/**
 * Start new chat session
 * POST /api/chat/start
 */
router.post('/start', async (req, res) => {
  try {
    const { model, systemPrompt, maxHistory } = req.body

    const session = new ChatSession({
      model,
      system: systemPrompt,
      maxHistory: parseInt(maxHistory) || 20
    })

    activeSessions.set(session.sessionId, session)

    res.json({
      success: true,
      data: {
        sessionId: session.sessionId,
        model: session.model,
        systemPrompt: session.systemPrompt
      }
    })
  } catch (error) {
    logger.error('Chat start API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Send message to chat session
 * POST /api/chat/message
 */
router.post('/message', async (req, res) => {
  try {
    const { sessionId, message } = req.body

    if (!sessionId || !message) {
      return res.status(400).json({
        error: 'Session ID and message are required'
      })
    }

    let session = activeSessions.get(sessionId)
    
    if (!session) {
      // Try to load from saved conversations
      try {
        session = await ChatSession.loadConversation(sessionId)
        activeSessions.set(sessionId, session)
      } catch (error) {
        return res.status(404).json({
          error: 'Chat session not found'
        })
      }
    }

    const response = await session.sendMessage(message)

    res.json({
      success: true,
      data: {
        message: response.message,
        model: response.model,
        usage: response.usage,
        sessionId
      }
    })
  } catch (error) {
    logger.error('Chat message API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Get chat history
 * GET /api/chat/:sessionId/history
 */
router.get('/:sessionId/history', async (req, res) => {
  try {
    const { sessionId } = req.params
    const { limit } = req.query

    let session = activeSessions.get(sessionId)
    
    if (!session) {
      try {
        session = await ChatSession.loadConversation(sessionId)
      } catch (error) {
        return res.status(404).json({
          error: 'Chat session not found'
        })
      }
    }

    let history = session.history
    if (limit) {
      history = history.slice(-parseInt(limit))
    }

    res.json({
      success: true,
      data: {
        sessionId,
        history,
        messageCount: session.history.length
      }
    })
  } catch (error) {
    logger.error('Chat history API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Save chat session
 * POST /api/chat/:sessionId/save
 */
router.post('/:sessionId/save', async (req, res) => {
  try {
    const { sessionId } = req.params

    const session = activeSessions.get(sessionId)
    if (!session) {
      return res.status(404).json({
        error: 'Chat session not found'
      })
    }

    await session.saveConversation()

    res.json({
      success: true,
      data: {
        sessionId,
        saved: true
      }
    })
  } catch (error) {
    logger.error('Chat save API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Clear chat history
 * POST /api/chat/:sessionId/clear
 */
router.post('/:sessionId/clear', async (req, res) => {
  try {
    const { sessionId } = req.params

    const session = activeSessions.get(sessionId)
    if (!session) {
      return res.status(404).json({
        error: 'Chat session not found'
      })
    }

    session.history = []

    res.json({
      success: true,
      data: {
        sessionId,
        cleared: true
      }
    })
  } catch (error) {
    logger.error('Chat clear API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Get chat session summary
 * GET /api/chat/:sessionId/summary
 */
router.get('/:sessionId/summary', async (req, res) => {
  try {
    const { sessionId } = req.params

    let session = activeSessions.get(sessionId)
    
    if (!session) {
      try {
        session = await ChatSession.loadConversation(sessionId)
      } catch (error) {
        return res.status(404).json({
          error: 'Chat session not found'
        })
      }
    }

    const summary = session.getSummary()

    res.json({
      success: true,
      data: summary
    })
  } catch (error) {
    logger.error('Chat summary API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * List all conversations
 * GET /api/chat/conversations
 */
router.get('/conversations', async (req, res) => {
  try {
    const conversations = await listConversations()

    res.json({
      success: true,
      data: conversations
    })
  } catch (error) {
    logger.error('List conversations API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Delete conversation
 * DELETE /api/chat/:sessionId
 */
router.delete('/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params

    await deleteConversation(sessionId)
    
    // Remove from active sessions
    activeSessions.delete(sessionId)

    res.json({
      success: true,
      data: {
        sessionId,
        deleted: true
      }
    })
  } catch (error) {
    logger.error('Delete conversation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Export conversation
 * GET /api/chat/:sessionId/export
 */
router.get('/:sessionId/export', async (req, res) => {
  try {
    const { sessionId } = req.params
    const { format } = req.query

    const exportPath = await exportConversation(sessionId, format || 'json')

    res.json({
      success: true,
      data: {
        sessionId,
        exportPath,
        format: format || 'json'
      }
    })
  } catch (error) {
    logger.error('Export conversation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

export default router
