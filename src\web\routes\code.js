import express from 'express'
import { processCode, getSupportedLanguages } from '../../modules/codeGeneration.js'
import { logger } from '../../utils/logger.js'

const router = express.Router()

/**
 * Generate code
 * POST /api/code/generate
 */
router.post('/generate', async (req, res) => {
  try {
    const { prompt, language, model } = req.body

    if (!prompt) {
      return res.status(400).json({
        error: 'Prompt is required'
      })
    }

    const result = await processCode(prompt, {
      action: 'generate',
      language,
      model
    })

    res.json({
      success: true,
      data: {
        code: result,
        language: language || 'javascript'
      }
    })
  } catch (error) {
    logger.error('Code generation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Review code
 * POST /api/code/review
 */
router.post('/review', async (req, res) => {
  try {
    const { code, language, model } = req.body

    if (!code) {
      return res.status(400).json({
        error: 'Code is required'
      })
    }

    const result = await processCode(code, {
      action: 'review',
      language,
      model
    })

    res.json({
      success: true,
      data: {
        review: result,
        language
      }
    })
  } catch (error) {
    logger.error('Code review API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Generate documentation
 * POST /api/code/document
 */
router.post('/document', async (req, res) => {
  try {
    const { code, language, type, model } = req.body

    if (!code) {
      return res.status(400).json({
        error: 'Code is required'
      })
    }

    const result = await processCode(code, {
      action: 'document',
      language,
      type,
      model
    })

    res.json({
      success: true,
      data: {
        documentation: result,
        type: type || 'api',
        language
      }
    })
  } catch (error) {
    logger.error('Code documentation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Generate tests
 * POST /api/code/test
 */
router.post('/test', async (req, res) => {
  try {
    const { code, language, model } = req.body

    if (!code) {
      return res.status(400).json({
        error: 'Code is required'
      })
    }

    const result = await processCode(code, {
      action: 'test',
      language,
      model
    })

    res.json({
      success: true,
      data: {
        tests: result,
        language
      }
    })
  } catch (error) {
    logger.error('Test generation API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Refactor code
 * POST /api/code/refactor
 */
router.post('/refactor', async (req, res) => {
  try {
    const { code, language, focus, model } = req.body

    if (!code) {
      return res.status(400).json({
        error: 'Code is required'
      })
    }

    const result = await processCode(code, {
      action: 'refactor',
      language,
      focus,
      model
    })

    res.json({
      success: true,
      data: {
        refactoredCode: result,
        focus: focus || 'general',
        language
      }
    })
  } catch (error) {
    logger.error('Code refactoring API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Upload and process code file
 * POST /api/code/upload
 */
router.post('/upload', (req, res, next) => {
  const upload = req.app.locals.upload
  upload.single('codeFile')(req, res, next)
}, async (req, res) => {
  try {
    const { action, language, focus, type } = req.body

    if (!req.file) {
      return res.status(400).json({
        error: 'Code file is required'
      })
    }

    const result = await processCode(req.file.path, {
      action: action || 'review',
      language,
      focus,
      type
    })

    res.json({
      success: true,
      data: {
        result,
        filename: req.file.originalname,
        action: action || 'review'
      }
    })
  } catch (error) {
    logger.error('Code upload API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

/**
 * Get supported languages
 * GET /api/code/languages
 */
router.get('/languages', async (req, res) => {
  try {
    const languages = getSupportedLanguages()
    res.json({
      success: true,
      data: languages
    })
  } catch (error) {
    logger.error('Get languages API error:', error)
    res.status(500).json({
      error: error.message
    })
  }
})

export default router
