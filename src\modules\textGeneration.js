import OpenAI from 'openai'
import Anthropic from '@anthropic-ai/sdk'
import axios from 'axios'
import { config } from '../config/index.js'
import { logger, logApiCall, logError } from '../utils/logger.js'
import { getMergedConfig } from '../utils/configManager.js'

/**
 * Initialize AI clients
 */
let openaiClient = null
let anthropicClient = null

function initializeClients () {
  if (config.apiKeys.openai && !openaiClient) {
    openaiClient = new OpenAI({
      apiKey: config.apiKeys.openai
    })
  }

  if (config.apiKeys.anthropic && !anthropicClient) {
    anthropicClient = new Anthropic({
      apiKey: config.apiKeys.anthropic
    })
  }
}

/**
 * Generate text using OpenAI models
 */
async function generateWithOpenAI (prompt, options = {}) {
  if (!openaiClient) {
    throw new Error('OpenAI API key not configured')
  }

  const startTime = Date.now()
  
  try {
    const response = await openaiClient.chat.completions.create({
      model: options.model || config.models.text.default,
      messages: [
        {
          role: 'system',
          content: options.systemPrompt || 'You are a helpful AI assistant.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: parseInt(options.maxLength) || 1000,
      temperature: parseFloat(options.temperature) || 0.7,
      top_p: options.topP || 1,
      frequency_penalty: options.frequencyPenalty || 0,
      presence_penalty: options.presencePenalty || 0
    })

    const duration = Date.now() - startTime
    const result = response.choices[0].message.content

    logApiCall('openai', options.model, prompt, result, duration)

    return {
      text: result,
      model: options.model,
      usage: response.usage,
      duration
    }
  } catch (error) {
    logError(error, { provider: 'openai', model: options.model })
    throw new Error(`OpenAI API error: ${error.message}`)
  }
}

/**
 * Generate text using Anthropic models
 */
async function generateWithAnthropic (prompt, options = {}) {
  if (!anthropicClient) {
    throw new Error('Anthropic API key not configured')
  }

  const startTime = Date.now()
  
  try {
    const response = await anthropicClient.messages.create({
      model: options.model || 'claude-3-haiku-20240307',
      max_tokens: parseInt(options.maxLength) || 1000,
      temperature: parseFloat(options.temperature) || 0.7,
      system: options.systemPrompt || 'You are a helpful AI assistant.',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    })

    const duration = Date.now() - startTime
    const result = response.content[0].text

    logApiCall('anthropic', options.model, prompt, result, duration)

    return {
      text: result,
      model: options.model,
      usage: response.usage,
      duration
    }
  } catch (error) {
    logError(error, { provider: 'anthropic', model: options.model })
    throw new Error(`Anthropic API error: ${error.message}`)
  }
}

/**
 * Generate text using local models (Ollama)
 */
async function generateWithLocal (prompt, options = {}) {
  const startTime = Date.now()
  
  try {
    const response = await axios.post(`${config.localModels.endpoint}/api/generate`, {
      model: options.model || config.localModels.defaultModel,
      prompt: prompt,
      system: options.systemPrompt,
      options: {
        temperature: parseFloat(options.temperature) || 0.7,
        top_p: options.topP || 1,
        max_tokens: parseInt(options.maxLength) || 1000
      },
      stream: false
    }, {
      timeout: config.localModels.timeout
    })

    const duration = Date.now() - startTime
    const result = response.data.response

    logApiCall('local', options.model, prompt, result, duration)

    return {
      text: result,
      model: options.model,
      usage: {
        prompt_tokens: response.data.prompt_eval_count || 0,
        completion_tokens: response.data.eval_count || 0,
        total_tokens: (response.data.prompt_eval_count || 0) + (response.data.eval_count || 0)
      },
      duration
    }
  } catch (error) {
    logError(error, { provider: 'local', model: options.model })
    throw new Error(`Local model error: ${error.message}`)
  }
}

/**
 * Determine provider from model name
 */
function getProviderFromModel (model) {
  if (model.startsWith('gpt-') || model.startsWith('text-')) {
    return 'openai'
  } else if (model.startsWith('claude-')) {
    return 'anthropic'
  } else {
    return 'local'
  }
}

/**
 * Main text generation function
 */
export async function generateText (prompt, options = {}) {
  if (!prompt || typeof prompt !== 'string') {
    throw new Error('Prompt is required and must be a string')
  }

  // Initialize clients
  initializeClients()

  // Get merged configuration
  const mergedConfig = await getMergedConfig()
  
  // Determine model and provider
  const model = options.model || mergedConfig.models.text.default
  const provider = options.provider || getProviderFromModel(model)

  logger.info(`Generating text with ${provider}:${model}`)

  // Generate based on provider
  let result
  switch (provider) {
    case 'openai':
      result = await generateWithOpenAI(prompt, { ...options, model })
      break
    case 'anthropic':
      result = await generateWithAnthropic(prompt, { ...options, model })
      break
    case 'local':
      result = await generateWithLocal(prompt, { ...options, model })
      break
    default:
      throw new Error(`Unsupported provider: ${provider}`)
  }

  return result
}

/**
 * Generate multiple text variations
 */
export async function generateTextVariations (prompt, options = {}) {
  const count = options.count || 3
  const variations = []

  for (let i = 0; i < count; i++) {
    try {
      const variation = await generateText(prompt, {
        ...options,
        temperature: (options.temperature || 0.7) + (i * 0.1) // Vary temperature
      })
      variations.push(variation)
    } catch (error) {
      logger.warn(`Failed to generate variation ${i + 1}:`, error.message)
    }
  }

  return variations
}

/**
 * Summarize text
 */
export async function summarizeText (text, options = {}) {
  const prompt = `Please summarize the following text in ${options.length || 'a few'} sentences:\n\n${text}`
  
  return await generateText(prompt, {
    ...options,
    systemPrompt: 'You are an expert at creating concise, accurate summaries.',
    temperature: 0.3
  })
}

/**
 * Translate text
 */
export async function translateText (text, targetLanguage, options = {}) {
  const prompt = `Translate the following text to ${targetLanguage}:\n\n${text}`
  
  return await generateText(prompt, {
    ...options,
    systemPrompt: `You are a professional translator. Translate accurately while preserving the original meaning and tone.`,
    temperature: 0.2
  })
}

/**
 * Rewrite text with different style
 */
export async function rewriteText (text, style, options = {}) {
  const prompt = `Rewrite the following text in a ${style} style:\n\n${text}`
  
  return await generateText(prompt, {
    ...options,
    systemPrompt: `You are an expert writer. Rewrite the text while maintaining its core message but adapting the style as requested.`,
    temperature: 0.5
  })
}

/**
 * Get available models for each provider
 */
export function getAvailableModels () {
  return {
    openai: Object.keys(config.models.text.openai || {}),
    anthropic: Object.keys(config.models.text.anthropic || {}),
    local: [config.localModels.defaultModel]
  }
}
