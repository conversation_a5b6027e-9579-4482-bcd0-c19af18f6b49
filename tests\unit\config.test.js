import { jest } from '@jest/globals'
import { config, validateConfig } from '../../src/config/index.js'

describe('Configuration Module', () => {
  describe('config object', () => {
    it('should have required properties', () => {
      expect(config).toHaveProperty('env')
      expect(config).toHaveProperty('server')
      expect(config).toHaveProperty('apiKeys')
      expect(config).toHaveProperty('models')
      expect(config).toHaveProperty('paths')
    })

    it('should have correct environment settings', () => {
      expect(config.env).toBe('test')
      expect(config.isTest).toBe(true)
      expect(config.isDevelopment).toBe(false)
      expect(config.isProduction).toBe(false)
    })

    it('should have server configuration', () => {
      expect(config.server).toHaveProperty('port')
      expect(config.server).toHaveProperty('host')
      expect(typeof config.server.port).toBe('number')
      expect(typeof config.server.host).toBe('string')
    })

    it('should have API keys configuration', () => {
      expect(config.apiKeys).toHaveProperty('openai')
      expect(config.apiKeys).toHaveProperty('anthropic')
      expect(config.apiKeys.openai).toBe('test-openai-key')
      expect(config.apiKeys.anthropic).toBe('test-anthropic-key')
    })

    it('should have models configuration', () => {
      expect(config.models).toHaveProperty('text')
      expect(config.models).toHaveProperty('image')
      expect(config.models).toHaveProperty('code')
      expect(config.models).toHaveProperty('chat')
      
      expect(config.models.text).toHaveProperty('default')
      expect(config.models.image).toHaveProperty('default')
      expect(config.models.code).toHaveProperty('default')
      expect(config.models.chat).toHaveProperty('default')
    })

    it('should have paths configuration', () => {
      expect(config.paths).toHaveProperty('root')
      expect(config.paths).toHaveProperty('src')
      expect(config.paths).toHaveProperty('uploads')
      expect(config.paths).toHaveProperty('data')
      expect(config.paths).toHaveProperty('logs')
      expect(config.paths).toHaveProperty('temp')
    })

    it('should have upload configuration', () => {
      expect(config.upload).toHaveProperty('maxFileSize')
      expect(config.upload).toHaveProperty('uploadDir')
      expect(config.upload).toHaveProperty('allowedTypes')
      expect(Array.isArray(config.upload.allowedTypes)).toBe(true)
    })

    it('should have feature flags', () => {
      expect(config.features).toHaveProperty('webInterface')
      expect(config.features).toHaveProperty('fileUpload')
      expect(config.features).toHaveProperty('conversationHistory')
      expect(config.features).toHaveProperty('rateLimit')
    })
  })

  describe('validateConfig', () => {
    it('should not throw error with valid API keys', () => {
      expect(() => validateConfig()).not.toThrow()
    })

    it('should throw error without any API keys', () => {
      // Temporarily remove API keys
      const originalOpenAI = config.apiKeys.openai
      const originalAnthropic = config.apiKeys.anthropic
      
      config.apiKeys.openai = null
      config.apiKeys.anthropic = null
      
      expect(() => validateConfig()).toThrow('Missing required configuration')
      
      // Restore API keys
      config.apiKeys.openai = originalOpenAI
      config.apiKeys.anthropic = originalAnthropic
    })

    it('should pass with only OpenAI key', () => {
      const originalAnthropic = config.apiKeys.anthropic
      config.apiKeys.anthropic = null
      
      expect(() => validateConfig()).not.toThrow()
      
      config.apiKeys.anthropic = originalAnthropic
    })

    it('should pass with only Anthropic key', () => {
      const originalOpenAI = config.apiKeys.openai
      config.apiKeys.openai = null
      
      expect(() => validateConfig()).not.toThrow()
      
      config.apiKeys.openai = originalOpenAI
    })
  })

  describe('environment detection', () => {
    it('should correctly identify test environment', () => {
      expect(config.isTest).toBe(true)
      expect(config.isDevelopment).toBe(false)
      expect(config.isProduction).toBe(false)
    })

    it('should have appropriate log level for test', () => {
      expect(config.logging.level).toBe('error')
    })
  })

  describe('default values', () => {
    it('should have sensible defaults for models', () => {
      expect(config.models.text.default).toBeTruthy()
      expect(config.models.image.default).toBeTruthy()
      expect(config.models.code.default).toBeTruthy()
      expect(config.models.chat.default).toBeTruthy()
    })

    it('should have reasonable file size limits', () => {
      expect(config.upload.maxFileSize).toBeGreaterThan(0)
      expect(config.upload.maxFileSize).toBeLessThanOrEqual(50 * 1024 * 1024) // 50MB max
    })

    it('should have rate limiting configuration', () => {
      expect(config.rateLimit.windowMs).toBeGreaterThan(0)
      expect(config.rateLimit.maxRequests).toBeGreaterThan(0)
    })
  })
})
