import { jest } from '@jest/globals'
import { generateText, summarizeText, translateText } from '../../src/modules/textGeneration.js'

describe('Text Generation Module', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('generateText', () => {
    it('should generate text with OpenAI', async () => {
      const prompt = 'Write a short story about a robot'
      const options = { model: 'gpt-3.5-turbo', temperature: 0.7 }

      const result = await generateText(prompt, options)

      expect(result).toHaveProperty('text')
      expect(result).toHaveProperty('model')
      expect(result).toHaveProperty('usage')
      expect(result.text).toBe('Mock response')
    })

    it('should throw error for empty prompt', async () => {
      await expect(generateText('')).rejects.toThrow('Prompt is required and must be a string')
    })

    it('should throw error for non-string prompt', async () => {
      await expect(generateText(123)).rejects.toThrow('Prompt is required and must be a string')
    })

    it('should use default model when none specified', async () => {
      const prompt = 'Test prompt'
      
      const result = await generateText(prompt)
      
      expect(result).toHaveProperty('text')
      expect(result).toHaveProperty('model')
    })

    it('should handle API errors gracefully', async () => {
      // Mock OpenAI to throw an error
      const OpenAI = (await import('openai')).default
      const mockClient = new OpenAI()
      mockClient.chat.completions.create.mockRejectedValue(new Error('API Error'))

      await expect(generateText('test prompt')).rejects.toThrow()
    })
  })

  describe('summarizeText', () => {
    it('should summarize text successfully', async () => {
      const text = 'This is a long text that needs to be summarized. It contains multiple sentences and paragraphs with various information that should be condensed into a shorter format.'
      
      const result = await summarizeText(text)
      
      expect(result).toHaveProperty('text')
      expect(result).toHaveProperty('model')
      expect(result.text).toBe('Mock response')
    })

    it('should accept length option', async () => {
      const text = 'Long text to summarize'
      const options = { length: 'short' }
      
      const result = await summarizeText(text, options)
      
      expect(result).toHaveProperty('text')
    })

    it('should throw error for empty text', async () => {
      await expect(summarizeText('')).rejects.toThrow()
    })
  })

  describe('translateText', () => {
    it('should translate text successfully', async () => {
      const text = 'Hello, how are you?'
      const targetLanguage = 'Spanish'
      
      const result = await translateText(text, targetLanguage)
      
      expect(result).toHaveProperty('text')
      expect(result).toHaveProperty('model')
      expect(result.text).toBe('Mock response')
    })

    it('should throw error for missing target language', async () => {
      const text = 'Hello world'
      
      await expect(translateText(text)).rejects.toThrow()
    })

    it('should throw error for empty text', async () => {
      await expect(translateText('', 'Spanish')).rejects.toThrow()
    })
  })

  describe('getAvailableModels', () => {
    it('should return available models', async () => {
      const { getAvailableModels } = await import('../../src/modules/textGeneration.js')
      
      const models = getAvailableModels()
      
      expect(models).toHaveProperty('openai')
      expect(models).toHaveProperty('anthropic')
      expect(models).toHaveProperty('local')
      expect(Array.isArray(models.openai)).toBe(true)
      expect(Array.isArray(models.anthropic)).toBe(true)
      expect(Array.isArray(models.local)).toBe(true)
    })
  })
})
